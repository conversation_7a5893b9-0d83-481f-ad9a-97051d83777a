<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - SkyStack</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/css/style.css">
</head>
<body class="font-poppins">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-lg border-b border-white/10">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <img src="/images/skystack-icon.svg" alt="SkyStack" class="w-10 h-10">
                    <span class="text-2xl font-bold text-white">SkyStack</span>
                </div>
                <a href="/" class="text-white hover:text-blue-400 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                </a>
            </div>
        </div>
    </nav>

    <!-- Success Page -->
    <div class="min-h-screen gradient-bg-1 pt-20">
        <div class="container mx-auto px-4 py-12">
            <div class="max-w-2xl mx-auto text-center">
                
                <!-- Success Animation -->
                <div class="mb-8">
                    <div class="w-32 h-32 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
                        <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h1 class="text-4xl font-bold text-white mb-4">Payment Successful! 🎉</h1>
                    <p class="text-xl text-white/80 mb-8">Thank you for your advance payment. We'll start working on your project soon!</p>
                </div>

                <!-- Payment Details -->
                <div class="bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-white/20 mb-8">
                    <h2 class="text-2xl font-bold text-white mb-6">Payment Details</h2>
                    
                    <div class="space-y-4 text-left">
                        <div class="flex justify-between items-center p-4 bg-white/5 rounded-lg">
                            <span class="text-white/80">Amount Paid:</span>
                            <span class="text-white font-bold text-lg">₹<span id="paidAmount">--</span></span>
                        </div>
                        
                        <div class="flex justify-between items-center p-4 bg-white/5 rounded-lg">
                            <span class="text-white/80">Transaction ID:</span>
                            <span class="text-white font-mono text-sm" id="transactionId">--</span>
                        </div>
                        
                        <div class="flex justify-between items-center p-4 bg-white/5 rounded-lg">
                            <span class="text-white/80">Payment Method:</span>
                            <span class="text-white">PhonePe UPI</span>
                        </div>
                        
                        <div class="flex justify-between items-center p-4 bg-white/5 rounded-lg">
                            <span class="text-white/80">Status:</span>
                            <span class="text-green-400 font-semibold">✅ Completed</span>
                        </div>
                        
                        <div class="flex justify-between items-center p-4 bg-white/5 rounded-lg">
                            <span class="text-white/80">Date & Time:</span>
                            <span class="text-white" id="paymentDate">--</span>
                        </div>
                    </div>
                </div>

                <!-- Next Steps -->
                <div class="bg-blue-500/20 border border-blue-500/30 rounded-2xl p-6 mb-8">
                    <h3 class="text-xl font-bold text-white mb-4">What Happens Next?</h3>
                    <div class="text-left space-y-3">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-white text-sm font-bold">1</span>
                            </div>
                            <p class="text-white/90">We'll review your project requirements from the form you submitted</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-white text-sm font-bold">2</span>
                            </div>
                            <p class="text-white/90">Our team will contact you within 24 hours to discuss project details</p>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-white text-sm font-bold">3</span>
                            </div>
                            <p class="text-white/90">We'll start development and keep you updated on progress</p>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="space-y-4">
                    <a href="/" class="block w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                        Back to Home
                    </a>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <a href="mailto:<EMAIL>" class="block text-center bg-white/10 hover:bg-white/20 text-white font-bold py-3 px-4 rounded-xl transition-all duration-300 border border-white/20">
                            Contact Us
                        </a>
                        <button onclick="window.print()" class="block w-full text-center bg-white/10 hover:bg-white/20 text-white font-bold py-3 px-4 rounded-xl transition-all duration-300 border border-white/20">
                            Print Receipt
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Get payment details from URL parameters or localStorage
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const amount = urlParams.get('amount') || localStorage.getItem('paymentAmount') || '0';
            const transactionId = urlParams.get('txnId') || 'TXN' + Date.now();
            
            // Update payment details
            document.getElementById('paidAmount').textContent = amount;
            document.getElementById('transactionId').textContent = transactionId;
            document.getElementById('paymentDate').textContent = new Date().toLocaleString();
            
            // Clear stored payment data
            localStorage.removeItem('paymentAmount');
            
            // Add confetti effect (optional)
            setTimeout(() => {
                // Simple confetti effect using CSS animations
                createConfetti();
            }, 500);
        });
        
        function createConfetti() {
            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7'];
            const confettiContainer = document.createElement('div');
            confettiContainer.style.position = 'fixed';
            confettiContainer.style.top = '0';
            confettiContainer.style.left = '0';
            confettiContainer.style.width = '100%';
            confettiContainer.style.height = '100%';
            confettiContainer.style.pointerEvents = 'none';
            confettiContainer.style.zIndex = '9999';
            document.body.appendChild(confettiContainer);
            
            for (let i = 0; i < 50; i++) {
                const confetti = document.createElement('div');
                confetti.style.position = 'absolute';
                confetti.style.width = '10px';
                confetti.style.height = '10px';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.left = Math.random() * 100 + '%';
                confetti.style.animationDuration = (Math.random() * 3 + 2) + 's';
                confetti.style.animationName = 'confetti-fall';
                confetti.style.animationTimingFunction = 'linear';
                confetti.style.animationFillMode = 'forwards';
                confettiContainer.appendChild(confetti);
            }
            
            // Remove confetti after animation
            setTimeout(() => {
                document.body.removeChild(confettiContainer);
            }, 5000);
        }
        
        // Add CSS animation for confetti
        const style = document.createElement('style');
        style.textContent = `
            @keyframes confetti-fall {
                0% {
                    transform: translateY(-100vh) rotate(0deg);
                    opacity: 1;
                }
                100% {
                    transform: translateY(100vh) rotate(720deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // Get payment amount from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const amount = urlParams.get('amount');

        // Update the amount display
        const paidAmountElement = document.getElementById('paidAmount');
        if (paidAmountElement && amount) {
            paidAmountElement.textContent = amount;
        } else if (paidAmountElement) {
            paidAmountElement.textContent = '0';
        }
    </script>
</body>
</html>
