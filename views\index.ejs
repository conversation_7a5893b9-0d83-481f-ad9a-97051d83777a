<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    
    <!-- SEO Meta Tags -->
    <meta name="description" content="SkyStack - Modern web development studio that builds fast, responsive, and beautiful websites. Code that performs. Design that delights.">
    <meta name="keywords" content="web development, website design, responsive websites, modern web apps, SkyStack, student projects">
    <meta name="author" content="SkyStack">
    <meta property="og:title" content="<%= title %>">
    <meta property="og:description" content="SkyStack - Code that performs. Design that delights. Modern web development studio."
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://skystack.com">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/images/skystack-icon.svg">
    <link rel="alternate icon" href="/favicon.ico">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'poppins': ['Poppins', 'sans-serif'],
                    },
                    colors: {
                        lavender: {
                            50: '#f8f7ff',
                            100: '#f0edff',
                            200: '#e6e0ff',
                            300: '#d4c7ff',
                            400: '#b8a3ff',
                            500: '#9b7eff',
                            600: '#8b5cf6',
                            700: '#7c3aed',
                            800: '#6d28d9',
                            900: '#5b21b6',
                        },
                        professional: {
                            50: '#faf9ff',
                            100: '#f4f1ff',
                            200: '#ebe5ff',
                            300: '#ddd1ff',
                            400: '#c7b3ff',
                            500: '#a78bfa',
                            600: '#8b5cf6',
                            700: '#7c3aed',
                            800: '#6d28d9',
                            900: '#581c87',
                        }
                    },
                    animation: {
                        'professional-float': 'professionalFloat 8s ease-in-out infinite',
                        'professional-float-delayed': 'professionalFloat 8s ease-in-out infinite 3s',
                        'fade-in-up': 'fadeInUp 1s ease-out',
                        'slide-in-right': 'slideInRight 0.8s ease-out',
                        'scale-bounce': 'scaleBounce 0.6s ease-out',
                        'gradient-shift': 'gradientShift 4s ease-in-out infinite',
                        'sophisticated-pulse': 'sophisticatedPulse 3s ease-in-out infinite',
                    },
                    boxShadow: {
                        'professional': '0 10px 40px rgba(139, 92, 246, 0.15)',
                        'lavender': '0 8px 32px rgba(230, 224, 255, 0.3)',
                        'elegant': '0 20px 60px rgba(139, 92, 246, 0.1)',
                    }
                }
            }
        }
    </script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/style.css">
</head>
<body class="font-inter text-gray-800 overflow-x-hidden">

    <!-- Enhanced Beautiful Lavender Background for Entire Website -->
    <div class="fixed inset-0 -z-10">
        <!-- Enhanced lavender gradient background -->
        <div class="absolute inset-0 bg-gradient-to-br from-lavender-200 via-lavender-100 to-lavender-50"></div>

        <!-- More visible lavender pattern overlay -->
        <div class="absolute inset-0 opacity-60">
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23DDD1FF" fill-opacity="0.8"%3E%3Ccircle cx="40" cy="40" r="3"/%3E%3Ccircle cx="20" cy="20" r="2"/%3E%3Ccircle cx="60" cy="20" r="2"/%3E%3Ccircle cx="20" cy="60" r="2"/%3E%3Ccircle cx="60" cy="60" r="2"/%3E%3Ccircle cx="10" cy="40" r="1"/%3E%3Ccircle cx="70" cy="40" r="1"/%3E%3Ccircle cx="40" cy="10" r="1"/%3E%3Ccircle cx="40" cy="70" r="1"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
        </div>

        <!-- More visible lavender floating elements -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute top-1/4 left-1/4 w-6 h-6 bg-lavender-300 rounded-full animate-professional-float opacity-70"></div>
            <div class="absolute top-3/4 right-1/4 w-8 h-8 bg-lavender-400 rounded-full animate-professional-float-delayed opacity-60"></div>
            <div class="absolute top-1/2 left-3/4 w-4 h-4 bg-lavender-500 rounded-full animate-professional-float opacity-80"></div>
            <div class="absolute top-1/3 right-1/3 w-7 h-7 bg-lavender-300 rounded-full animate-professional-float-delayed opacity-50"></div>

            <!-- Additional visible elements -->
            <div class="absolute top-2/3 left-1/6 w-3 h-3 bg-lavender-400 rounded-full animate-pulse opacity-60"></div>
            <div class="absolute top-1/6 right-2/3 w-5 h-5 bg-lavender-500 rounded-full animate-pulse opacity-50"></div>
            <div class="absolute bottom-1/4 left-1/2 w-3 h-3 bg-lavender-300 rounded-full animate-pulse opacity-70"></div>
            <div class="absolute top-1/5 left-1/5 w-4 h-4 bg-lavender-200 rounded-full animate-professional-float opacity-40"></div>
            <div class="absolute bottom-1/3 right-1/5 w-5 h-5 bg-lavender-400 rounded-full animate-professional-float-delayed opacity-60"></div>
        </div>

        <!-- Enhanced gradient overlays for more depth -->
        <div class="absolute inset-0 bg-gradient-to-t from-lavender-100/40 via-transparent to-lavender-200/30"></div>
        <div class="absolute inset-0 bg-gradient-to-r from-lavender-100/30 via-transparent to-lavender-100/30"></div>
        <div class="absolute inset-0 bg-gradient-to-br from-transparent via-lavender-50/20 to-lavender-100/40"></div>
    </div>
    <!-- Professional Header/Navbar -->
    <header class="fixed top-0 left-0 right-0 bg-white/90 backdrop-blur-sm shadow-lg z-50 transition-all duration-300 border-b border-lavender-200/30">
        <nav class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <!-- Lavender Logo -->
                <div class="flex items-center space-x-3 group">
                    <img src="/images/skystack-icon.svg" alt="SkyStack Logo" class="h-10 w-10 transform group-hover:scale-110 transition-all duration-300 filter brightness-0 saturate-100" style="filter: brightness(0) saturate(100%) invert(45%) sepia(84%) saturate(2270%) hue-rotate(240deg) brightness(102%) contrast(92%);">
                    <span class="text-2xl font-bold text-lavender-700">SkyStack</span>
                </div>

                <!-- Lavender Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-2">
                    <a href="#home" class="nav-link text-lavender-700 hover:text-lavender-800 transition-all duration-300 px-4 py-2 rounded-xl hover:bg-lavender-100 font-medium">Home</a>
                    <a href="#about" class="nav-link text-lavender-700 hover:text-lavender-800 transition-all duration-300 px-4 py-2 rounded-xl hover:bg-lavender-100 font-medium">About</a>
                    <a href="#services" class="nav-link text-lavender-700 hover:text-lavender-800 transition-all duration-300 px-4 py-2 rounded-xl hover:bg-lavender-100 font-medium">Services</a>
                    <a href="#projects" class="nav-link text-lavender-700 hover:text-lavender-800 transition-all duration-300 px-4 py-2 rounded-xl hover:bg-lavender-100 font-medium">Projects</a>
                    <a href="#contact" class="nav-link text-lavender-700 hover:text-lavender-800 transition-all duration-300 px-4 py-2 rounded-xl hover:bg-lavender-100 font-medium">Contact</a>
                    <a href="#payment" class="nav-link bg-lavender-500 text-white hover:bg-lavender-600 transition-all duration-300 px-6 py-2 rounded-xl font-semibold transform hover:scale-105">Payment</a>
                </div>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-btn" class="md:hidden p-2 rounded-lg hover:bg-white/10 transition-colors text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>

            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4 border-t border-slate-700/50">
                <div class="flex flex-col space-y-4 pt-4">
                    <a href="#home" class="nav-link text-white hover:text-blue-400 transition-colors duration-300 px-3 py-2 rounded-lg hover:bg-white/10">Home</a>
                    <a href="#about" class="nav-link text-white hover:text-blue-400 transition-colors duration-300 px-3 py-2 rounded-lg hover:bg-white/10">About</a>
                    <a href="#services" class="nav-link text-white hover:text-blue-400 transition-colors duration-300 px-3 py-2 rounded-lg hover:bg-white/10">Services</a>
                    <a href="#projects" class="nav-link text-white hover:text-blue-400 transition-colors duration-300 px-3 py-2 rounded-lg hover:bg-white/10">Projects</a>
                    <a href="#contact" class="nav-link text-white hover:text-blue-400 transition-colors duration-300 px-3 py-2 rounded-lg hover:bg-white/10">Contact</a>
                    <a href="#payment" class="nav-link text-white hover:text-purple-400 transition-colors duration-300 px-3 py-2 rounded-lg hover:bg-purple-500/20 border border-purple-500/30">Payment</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Scroll Progress Indicator -->
    <div class="scroll-progress"></div>

    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="text-center text-white">
            <div class="mb-6">
                <img src="/images/skystack-logo-white.svg" alt="SkyStack Logo" class="h-12 mx-auto mb-4">
            </div>
            <div class="loader mb-4"></div>
            <p class="text-lg">Loading amazing experience...</p>
        </div>
    </div>

    <!-- Dark Mode Toggle -->
    <button id="dark-mode-toggle" class="dark-mode-toggle">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
        </svg>
    </button>

    <!-- Main Content -->
    <main class="pt-20">
        <!-- Professional Lavender Hero Section -->
        <section id="home" class="min-h-screen flex items-center justify-center relative overflow-hidden">
            <!-- Subtle professional floating elements -->
            <div class="absolute top-20 left-10 w-16 h-16 bg-lavender-200/40 rounded-full animate-professional-float opacity-60"></div>
            <div class="absolute top-40 right-20 w-12 h-12 bg-lavender-300/40 rounded-full animate-professional-float-delayed opacity-50"></div>
            <div class="absolute bottom-40 left-20 w-8 h-8 bg-lavender-400/40 rounded-full animate-professional-float opacity-40"></div>

            <div class="container mx-auto px-6 text-center relative z-10">
                <div class="max-w-5xl mx-auto">
                    <!-- Professional SkyStack Logo -->
                    <div class="mb-12 animate-fade-in-up">
                        <img src="/images/skystack-icon.svg" alt="SkyStack Logo" class="w-24 h-24 mx-auto transform hover:scale-110 transition-all duration-500 filter brightness-0 saturate-100%" style="filter: brightness(0) saturate(100%) invert(45%) sepia(84%) saturate(2270%) hue-rotate(240deg) brightness(102%) contrast(92%);">
                    </div>

                    <!-- Professional animated title -->
                    <h1 class="text-5xl md:text-7xl font-bold font-poppins mb-6 text-lavender-700 animate-fade-in-up" style="animation-delay: 0.2s;">
                        Welcome to <span class="text-lavender-800">SkyStack</span>
                    </h1>

                    <!-- Professional subtitle -->
                    <p class="text-xl md:text-2xl text-lavender-600 mb-8 font-light animate-fade-in-up leading-relaxed" style="animation-delay: 0.4s;">
                        Code that performs. Design that delights.
                    </p>

                    <!-- Professional CTA button -->
                    <div class="animate-fade-in-up" style="animation-delay: 0.6s;">
                        <button onclick="scrollToSection('contact')" class="bg-lavender-500 hover:bg-lavender-600 text-white px-8 py-4 rounded-full text-lg font-semibold shadow-lg transform hover:scale-105 transition-all duration-300">
                            Get Started
                        </button>
                    </div>

                    <!-- Professional features -->
                    <div class="mt-12 grid md:grid-cols-3 gap-8 animate-fade-in-up" style="animation-delay: 0.8s;">
                        <div class="text-lavender-600">
                            <div class="text-2xl mb-2">✨</div>
                            <div class="font-medium">Professional Development</div>
                        </div>
                        <div class="text-lavender-600">
                            <div class="text-2xl mb-2">🎨</div>
                            <div class="font-medium">Beautiful Design</div>
                        </div>
                        <div class="text-lavender-600">
                            <div class="text-2xl mb-2">⚡</div>
                            <div class="font-medium">Fast Performance</div>
                        </div>
                    </div>
                            Let's Build Together 🚀
                        </button>
                    </div>
                </div>
            </div>

            <!-- Animated Background Elements -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none">
                <div class="absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
                <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl"></div>
            </div>
        </section>

        <!-- Lavender About Section -->
        <section id="about" class="py-20 relative">
            <div class="container mx-auto px-6 relative z-10">
                <div class="grid md:grid-cols-2 gap-16 items-center">
                    <div>
                        <h2 class="text-5xl font-bold font-poppins mb-8 text-lavender-700">About SkyStack</h2>
                        <p class="text-xl text-lavender-600 mb-8 leading-relaxed">
                            SkyStack is a modern web development studio that builds fast, responsive, and beautiful websites for clients worldwide. We specialize in creating digital experiences that not only look stunning but also perform exceptionally well.
                        </p>
                        <p class="text-xl text-lavender-600 mb-8 leading-relaxed">
                            Our team combines cutting-edge technology with creative design to deliver websites that help businesses grow and succeed in the digital landscape. From simple landing pages to complex web applications, we've got you covered.
                        </p>

                        <!-- Lavender Stats Counter -->
                        <div class="grid grid-cols-3 gap-8 mb-8">
                            <div class="text-center bg-lavender-100 p-4 rounded-xl">
                                <div class="counter text-lavender-700 text-3xl font-bold" data-target="25">0</div>
                                <p class="text-lavender-600 text-sm font-medium">Projects</p>
                            </div>
                            <div class="text-center bg-lavender-100 p-4 rounded-xl">
                                <div class="counter text-lavender-700 text-3xl font-bold" data-target="100">0</div>
                                <p class="text-lavender-600 text-sm font-medium">Success Rate</p>
                            </div>
                            <div class="text-center bg-lavender-100 p-4 rounded-xl">
                                <div class="counter text-lavender-700 text-3xl font-bold" data-target="24">0</div>
                                <p class="text-lavender-600 text-sm font-medium">Hour Support</p>
                            </div>
                        </div>

                        <div class="flex items-center space-x-4 bg-lavender-100 p-4 rounded-xl">
                            <div class="w-12 h-12 bg-lavender-500 rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="text-lavender-700 font-semibold">Quality code, delivered on time ⚡</span>
                        </div>
                    </div>
                    <div class="flex justify-center">
                        <div class="w-full max-w-md h-80 bg-lavender-100 rounded-2xl shadow-xl flex items-center justify-center border border-lavender-200">
                            <div class="text-center text-lavender-700">
                                <div class="mb-6">
                                    <div class="logo-glow-ring"></div>
                                    <img src="/images/skystack-icon.svg" alt="SkyStack Logo" class="w-24 h-24 mx-auto card-logo">
                                </div>
                                <p class="text-xl font-semibold">Code that Performs</p>
                                <p class="text-white/80">Design that Delights</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Background Elements -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none">
                <div class="absolute top-20 left-10 w-32 h-32 bg-white/5 rounded-full blur-2xl"></div>
                <div class="absolute bottom-20 right-10 w-40 h-40 bg-white/5 rounded-full blur-2xl"></div>
            </div>
        </section>

        <!-- Technology Stack Section -->
        <section class="py-20 geometric-bg">
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold font-poppins mb-4 text-white">Our Technology Stack</h2>
                    <p class="text-xl text-white/80 max-w-2xl mx-auto">
                        We use cutting-edge technologies to build amazing digital experiences
                    </p>
                </div>

                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
                    <!-- Tech Stack Items -->
                    <div class="tech-item bg-white/10 backdrop-blur-sm p-6 rounded-xl text-center hover:bg-white/20 transition-all duration-300 floating">
                        <div class="text-4xl mb-3">⚛️</div>
                        <p class="text-white font-medium">React</p>
                    </div>
                    <div class="tech-item bg-white/10 backdrop-blur-sm p-6 rounded-xl text-center hover:bg-white/20 transition-all duration-300 floating" style="animation-delay: 0.2s;">
                        <div class="text-4xl mb-3">🟢</div>
                        <p class="text-white font-medium">Node.js</p>
                    </div>
                    <div class="tech-item bg-white/10 backdrop-blur-sm p-6 rounded-xl text-center hover:bg-white/20 transition-all duration-300 floating" style="animation-delay: 0.4s;">
                        <div class="text-4xl mb-3">🎨</div>
                        <p class="text-white font-medium">Tailwind</p>
                    </div>
                    <div class="tech-item bg-white/10 backdrop-blur-sm p-6 rounded-xl text-center hover:bg-white/20 transition-all duration-300 floating" style="animation-delay: 0.6s;">
                        <div class="text-4xl mb-3">🔥</div>
                        <p class="text-white font-medium">Firebase</p>
                    </div>
                    <div class="tech-item bg-white/10 backdrop-blur-sm p-6 rounded-xl text-center hover:bg-white/20 transition-all duration-300 floating" style="animation-delay: 0.8s;">
                        <div class="text-4xl mb-3">📱</div>
                        <p class="text-white font-medium">Mobile</p>
                    </div>
                    <div class="tech-item bg-white/10 backdrop-blur-sm p-6 rounded-xl text-center hover:bg-white/20 transition-all duration-300 floating" style="animation-delay: 1s;">
                        <div class="text-4xl mb-3">☁️</div>
                        <p class="text-white font-medium">Cloud</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Lavender Services Section -->
        <section id="services" class="py-24 relative">
            <div class="container mx-auto px-6 relative z-10">
                <div class="text-center mb-20">
                    <h2 class="text-5xl md:text-6xl font-bold font-poppins mb-6 text-lavender-700">Our Services</h2>
                    <p class="text-xl md:text-2xl text-lavender-600 max-w-3xl mx-auto leading-relaxed">
                        We offer comprehensive web development services to bring your digital vision to life ✨
                    </p>
                </div>

                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-10">
                    <!-- Professional Service 1 -->
                    <div class="bg-white p-8 rounded-2xl shadow-lg professional-hover border border-lavender-200">
                        <div class="w-16 h-16 bg-lavender-500 rounded-xl flex items-center justify-center mb-6 transition-all duration-300">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-4 text-lavender-700">Static Websites</h3>
                        <p class="text-lavender-600 leading-relaxed">Fast-loading, SEO-optimized static websites perfect for portfolios, landing pages, and business sites.</p>
                    </div>

                    <!-- Professional Service 2 -->
                    <div class="bg-white p-8 rounded-2xl shadow-lg professional-hover border border-lavender-200">
                        <div class="w-16 h-16 bg-lavender-500 rounded-xl flex items-center justify-center mb-6 transition-all duration-300">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-4 text-lavender-700">Dynamic Sites</h3>
                        <p class="text-lavender-600 leading-relaxed">Interactive web applications with databases, user authentication, and real-time features.</p>
                    </div>

                    <!-- Professional Service 3 -->
                    <div class="bg-white p-8 rounded-2xl shadow-lg professional-hover border border-lavender-200">
                        <div class="w-16 h-16 bg-lavender-500 rounded-xl flex items-center justify-center mb-6 transition-all duration-300">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-4 text-lavender-700">Admin Dashboard</h3>
                        <p class="text-lavender-600 leading-relaxed">Powerful admin panels and dashboards for managing your business data and operations efficiently.</p>
                    </div>

                    <!-- Professional Service 4 -->
                    <div class="bg-white p-8 rounded-2xl shadow-lg professional-hover border border-lavender-200">
                        <div class="w-16 h-16 bg-lavender-500 rounded-xl flex items-center justify-center mb-6 transition-all duration-300">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-4 text-lavender-700">SEO + Deployment</h3>
                        <p class="text-lavender-600 leading-relaxed">Complete SEO optimization and seamless deployment to get your website live and discoverable.</p>
                    </div>

                    <!-- Professional Service 5 -->
                    <div class="bg-white p-8 rounded-2xl shadow-lg professional-hover border border-lavender-200">
                        <div class="w-16 h-16 bg-lavender-500 rounded-xl flex items-center justify-center mb-6 transition-all duration-300">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-4 text-lavender-700">Hosting Setup</h3>
                        <p class="text-lavender-600 leading-relaxed">Professional hosting solutions with SSL certificates, CDN, and ongoing maintenance support.</p>
                    </div>

                    <!-- Professional Service 6 -->
                    <div class="bg-white p-8 rounded-2xl shadow-lg professional-hover border border-lavender-200">
                        <div class="w-16 h-16 bg-lavender-500 rounded-xl flex items-center justify-center mb-6 transition-all duration-300">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-4 text-lavender-700">Fully Custom-Built Website</h3>
                        <p class="text-lavender-600 leading-relaxed">Completely custom websites tailored to your unique requirements and brand identity.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Lavender Projects Section -->
        <section id="projects" class="py-20 relative">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold font-poppins mb-4 text-lavender-700">Our Projects</h2>
                    <p class="text-xl text-lavender-600 max-w-2xl mx-auto">
                        Amazing projects coming soon! We're building incredible digital experiences 🚀
                    </p>
                </div>

                <div class="text-center">
                    <div class="max-w-2xl mx-auto bg-white rounded-3xl p-12 border border-lavender-200 shadow-lg">
                        <div class="text-6xl mb-6">🚧</div>
                        <h3 class="text-2xl font-bold text-lavender-700 mb-4">Projects Coming Soon!</h3>
                        <p class="text-lavender-600 mb-8 leading-relaxed">
                            We're currently working on some amazing projects that will showcase our expertise in web development,
                            mobile apps, and digital solutions. Stay tuned for our portfolio updates!
                        </p>

                        <!-- Coming Soon Features -->
                        <div class="grid md:grid-cols-3 gap-6 mb-8">
                            <div class="text-center">
                                <div class="text-3xl mb-2">🌐</div>
                                <p class="text-lavender-600 font-medium">Web Applications</p>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl mb-2">📱</div>
                                <p class="text-lavender-600 font-medium">Mobile Apps</p>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl mb-2">🎨</div>
                                <p class="text-lavender-600 font-medium">UI/UX Design</p>
                            </div>
                        </div>

                        <button onclick="scrollToSection('contact')" class="bg-lavender-500 hover:bg-lavender-600 text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                            Start Your Project Today! ✨
                        </button>
                    </div>
                </div>
            </div>

            <!-- Background Elements -->
            <div class="absolute inset-0 overflow-hidden pointer-events-none">
                <div class="absolute top-20 left-10 w-32 h-32 bg-white/5 rounded-full blur-2xl floating"></div>
                <div class="absolute bottom-20 right-10 w-40 h-40 bg-white/5 rounded-full blur-2xl floating" style="animation-delay: 2s;"></div>
            </div>
        </section>

        <!-- Lavender Student Testimonials Section -->
        <section class="py-20 relative">
            <div class="container mx-auto px-6">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold font-poppins mb-4 text-lavender-700">Student Project Reviews</h2>
                    <p class="text-xl text-lavender-600 max-w-2xl mx-auto">
                        See what students say about their project experiences with SkyStack! 🎓
                    </p>
                </div>

                <div class="grid md:grid-cols-3 gap-8">
                    <!-- Student Review 1 -->
                    <div class="bg-white p-8 rounded-2xl border border-lavender-200 shadow-lg professional-hover">
                        <div class="text-4xl mb-4">⭐⭐⭐⭐⭐</div>
                        <p class="text-lavender-600 mb-6 italic">
                            "Amazing help with my final year project! The website turned out exactly how I imagined. Clean code and great design!"
                        </p>
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-lavender-500 rounded-full flex items-center justify-center mr-4">
                                <span class="text-white font-bold">AP</span>
                            </div>
                            <div>
                                <p class="text-lavender-700 font-semibold">Arjun Patel</p>
                                <p class="text-lavender-600 text-sm">Computer Science Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- Student Review 2 -->
                    <div class="bg-white p-8 rounded-2xl border border-lavender-200 shadow-lg professional-hover">
                        <div class="text-4xl mb-4">⭐⭐⭐⭐⭐</div>
                        <p class="text-lavender-600 mb-6 italic">
                            "Perfect for my college project! Fast delivery, responsive design, and helped me understand the code. Highly recommend!"
                        </p>
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-lavender-500 rounded-full flex items-center justify-center mr-4">
                                <span class="text-white font-bold">PS</span>
                            </div>
                            <div>
                                <p class="text-lavender-700 font-semibold">Priya Sharma</p>
                                <p class="text-lavender-600 text-sm">IT Engineering Student</p>
                            </div>
                        </div>
                    </div>

                    <!-- Student Review 3 -->
                    <div class="bg-white p-8 rounded-2xl border border-lavender-200 shadow-lg professional-hover">
                        <div class="text-4xl mb-4">⭐⭐⭐⭐⭐</div>
                        <p class="text-lavender-600 mb-6 italic">
                            "Got an A+ on my web development project! The team explained everything clearly and delivered on time. Thank you SkyStack!"
                        </p>
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-lavender-500 rounded-full flex items-center justify-center mr-4">
                                <span class="text-white font-bold">RK</span>
                            </div>
                            <div>
                                <p class="text-lavender-700 font-semibold">Rahul Kumar</p>
                                <p class="text-lavender-600 text-sm">MCA Student</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Call to Action -->
                <div class="text-center mt-12">
                    <p class="text-lavender-600 text-lg mb-6">Need help with your project too?</p>
                    <button onclick="scrollToSection('contact')" class="bg-lavender-500 hover:bg-lavender-600 text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                        Start Your Project Today! 📚
                    </button>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="py-20 geometric-bg">
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold font-poppins mb-4 text-white">Frequently Asked Questions</h2>
                    <p class="text-xl text-white/80 max-w-2xl mx-auto">
                        Got questions? We've got answers! 💡
                    </p>
                </div>

                <div class="max-w-3xl mx-auto space-y-6">
                    <!-- FAQ Item 1 -->
                    <div class="faq-item bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
                        <button class="faq-question w-full text-left p-6 text-white font-semibold text-lg hover:bg-white/5 transition-colors">
                            How long does it take to complete my project? ⏰
                        </button>
                        <div class="faq-answer hidden p-6 pt-0 text-white/90">
                            Typically 3-7 days depending on complexity. Simple projects can be done in 2-3 days, while complex applications may take up to a week.
                        </div>
                    </div>

                    <!-- FAQ Item 2 -->
                    <div class="faq-item bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
                        <button class="faq-question w-full text-left p-6 text-white font-semibold text-lg hover:bg-white/5 transition-colors">
                            Do you help with project documentation? 📝
                        </button>
                        <div class="faq-answer hidden p-6 pt-0 text-white/90">
                            Yes! We provide complete project documentation, code comments, and can help you understand how everything works for your presentation.
                        </div>
                    </div>

                    <!-- FAQ Item 3 -->
                    <div class="faq-item bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
                        <button class="faq-question w-full text-left p-6 text-white font-semibold text-lg hover:bg-white/5 transition-colors">
                            What's included in student projects? 🎓
                        </button>
                        <div class="faq-answer hidden p-6 pt-0 text-white/90">
                            Complete source code, responsive design, documentation, project report template, and explanation of key concepts used.
                        </div>
                    </div>

                    <!-- FAQ Item 4 -->
                    <div class="faq-item bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
                        <button class="faq-question w-full text-left p-6 text-white font-semibold text-lg hover:bg-white/5 transition-colors">
                            Can you customize based on my requirements? 🎨
                        </button>
                        <div class="faq-answer hidden p-6 pt-0 text-white/90">
                            Absolutely! We tailor each project to your specific requirements, college guidelines, and academic standards.
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Lavender Contact Section -->
        <section id="contact" class="py-24 relative">

            <div class="container mx-auto px-6 relative z-10">
                <div class="text-center mb-20">
                    <h2 class="text-5xl md:text-6xl font-bold font-poppins mb-6 text-lavender-700">Get In Touch</h2>
                    <p class="text-xl md:text-2xl text-lavender-600 max-w-3xl mx-auto leading-relaxed">
                        Ready to start your project? Let's discuss your ideas and bring them to life ✨
                    </p>
                </div>

                <div class="grid lg:grid-cols-2 gap-16 max-w-7xl mx-auto">
                    <!-- Lavender Contact Form -->
                    <div class="bg-white p-12 rounded-3xl shadow-lg border border-lavender-200">
                        <h3 class="text-3xl font-bold mb-8 text-lavender-700">Send us a message</h3>
                        <!-- Google Form Embedded -->
                        <div class="w-full">
                            <iframe src="https://docs.google.com/forms/d/e/1FAIpQLSe26RFGrvIBDwVTvRixPBNtM3EV_uEEvvfXAgg1BCHUciVjOQ/viewform?embedded=true"
                                    width="100%"
                                    height="600"
                                    frameborder="0"
                                    marginheight="0"
                                    marginwidth="0"
                                    class="rounded-lg">
                                Loading…
                            </iframe>
                        </div>

                        <!-- Alternative: Direct Link Button -->
                        <div class="mt-4 text-center">
                            <a href="https://docs.google.com/forms/d/e/1FAIpQLSe26RFGrvIBDwVTvRixPBNtM3EV_uEEvvfXAgg1BCHUciVjOQ/viewform"
                               target="_blank"
                               class="inline-block bg-gradient-to-r from-sky-blue to-sky-dark text-white py-3 px-6 rounded-lg font-semibold hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                                Open Form in New Tab
                            </a>
                        </div>
                    </div>

                    <!-- Contact Info -->
                    <div class="space-y-8">
                        <div class="bg-white p-8 rounded-2xl shadow-lg">
                            <h3 class="text-2xl font-semibold mb-6 text-gray-900">Contact Information</h3>
                            <div class="space-y-6">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.588z"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="font-semibold text-gray-900">WhatsApp</p>
                                        <a href="https://wa.me/918792866211" class="text-sky-blue hover:text-sky-dark transition-colors">+91 8792866211</a>
                                    </div>
                                </div>

                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-sky-blue rounded-full flex items-center justify-center">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <p class="font-semibold text-gray-900">Email</p>
                                        <a href="mailto:<EMAIL>" class="text-sky-blue hover:text-sky-dark transition-colors"><EMAIL></a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Section -->
                        <div class="bg-white p-8 rounded-2xl shadow-lg">
                            <h3 class="text-2xl font-semibold mb-6 text-gray-900">Payment Options</h3>
                            <p class="text-gray-600 mb-4">We accept payment via UPI, Razorpay, or PayPal after project confirmation.</p>
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700 font-medium">Secure & Flexible Payment Methods</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>



        <!-- WhatsApp Contact Section (Hidden by default) -->
        <section id="whatsapp-contact" class="py-20 relative hidden">
            <div class="container mx-auto px-4">
                <div class="text-center mb-16">
                    <div class="w-24 h-24 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.785"/>
                        </svg>
                    </div>
                    <h2 class="text-4xl font-bold font-poppins mb-4 text-lavender-700">Contact SkyStack Team 💬</h2>
                    <p class="text-xl text-lavender-600 max-w-2xl mx-auto">
                        Let's discuss your project details and confirm your order via WhatsApp
                    </p>
                </div>

                <div class="max-w-2xl mx-auto">
                    <!-- WhatsApp Contact Card -->
                    <div class="bg-white rounded-2xl shadow-lg p-8 border border-lavender-200 mb-8">
                        <div class="text-center mb-8">
                            <h3 class="text-2xl font-bold text-lavender-700 mb-4">Message Us on WhatsApp</h3>
                            <p class="text-lavender-600 mb-6">We'll respond within 2 hours and discuss:</p>

                            <div class="grid md:grid-cols-2 gap-4 text-left mb-8">
                                <div class="bg-white/5 rounded-lg p-4">
                                    <div class="flex items-center mb-2">
                                        <svg class="w-5 h-5 text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        <span class="text-white font-semibold">Project Scope</span>
                                    </div>
                                    <p class="text-white/70 text-sm">Detailed requirements review</p>
                                </div>

                                <div class="bg-white/5 rounded-lg p-4">
                                    <div class="flex items-center mb-2">
                                        <svg class="w-5 h-5 text-blue-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <span class="text-white font-semibold">Timeline</span>
                                    </div>
                                    <p class="text-white/70 text-sm">Project delivery schedule</p>
                                </div>

                                <div class="bg-white/5 rounded-lg p-4">
                                    <div class="flex items-center mb-2">
                                        <svg class="w-5 h-5 text-purple-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                        <span class="text-white font-semibold">Pricing</span>
                                    </div>
                                    <p class="text-white/70 text-sm">Custom quote & payment plan</p>
                                </div>

                                <div class="bg-white/5 rounded-lg p-4">
                                    <div class="flex items-center mb-2">
                                        <svg class="w-5 h-5 text-orange-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                        </svg>
                                        <span class="text-white font-semibold">Next Steps</span>
                                    </div>
                                    <p class="text-white/70 text-sm">Project kickoff process</p>
                                </div>
                            </div>
                        </div>

                        <!-- WhatsApp Button -->
                        <div class="text-center">
                            <a href="https://wa.me/918792866211?text=Hi%20SkyStack%20Team!%20%F0%9F%91%8B%0A%0AI%20just%20submitted%20my%20project%20requirements%20form%20and%20would%20like%20to%20discuss:%0A%0A%F0%9F%93%8B%20Project%20scope%20and%20requirements%0A%E2%8F%B0%20Timeline%20and%20delivery%20schedule%0A%F0%9F%92%B0%20Pricing%20and%20payment%20options%0A%F0%9F%9A%80%20Next%20steps%20to%20get%20started%0A%0APlease%20let%20me%20know%20when%20we%20can%20discuss%20this%20further.%20Looking%20forward%20to%20working%20with%20you!"
                               target="_blank"
                               class="inline-block bg-green-500 hover:bg-green-600 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                                <svg class="w-6 h-6 mr-2 inline-block" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.785"/>
                                </svg>
                                Message on WhatsApp
                            </a>

                            <p class="text-white/70 text-sm mt-4">
                                <svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                We typically respond within 2 hours
                            </p>
                        </div>
                    </div>

                    <!-- Continue to Payment Button -->
                    <div class="text-center">
                        <button id="proceedToPayment" class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                            <svg class="w-5 h-5 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Continue to Payment
                        </button>
                        <p class="text-white/70 text-sm mt-2">After WhatsApp discussion, proceed with secure payment</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Lavender Payment Section -->
        <section id="payment" class="py-20 relative hidden">
            <div class="container mx-auto px-6">
                <div class="text-center mb-12">
                    <h1 class="text-4xl font-bold text-lavender-700 mb-4">Payment Details</h1>
                    <p class="text-xl text-lavender-600">Complete your advance payment securely</p>
                </div>

                <div class="max-w-2xl mx-auto">
                    <!-- Payment Options -->
                    <div class="bg-white/95 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-gray-200 mb-8">
                        <h2 class="text-2xl font-bold text-gray-800 mb-6 text-center">Choose Payment Method</h2>

                        <!-- PhonePe Option -->
                        <div class="payment-option active" data-method="phonepe">
                            <div class="flex items-center justify-between p-4 bg-purple-50 rounded-xl border border-purple-200 cursor-pointer hover:bg-purple-100 transition-all">
                                <div class="flex items-center space-x-4">
                                    <div class="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center">
                                        <span class="text-white font-bold text-lg">₹</span>
                                    </div>
                                    <div>
                                        <h3 class="text-gray-800 font-semibold">PhonePe UPI</h3>
                                        <p class="text-gray-600 text-sm">Pay using PhonePe UPI</p>
                                    </div>
                                </div>
                                <div class="w-6 h-6 border-2 border-purple-500 rounded-full flex items-center justify-center">
                                    <div class="w-3 h-3 bg-purple-500 rounded-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Details -->
                    <div class="bg-white/95 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-gray-200">
                        <div class="text-center mb-8">
                            <div class="w-24 h-24 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-800 mb-2">PhonePe Payment</h3>
                            <p class="text-gray-600">Scan QR or use UPI ID</p>
                        </div>

                        <!-- Amount Input -->
                        <div class="mb-6">
                            <label for="paymentAmount" class="block text-gray-800 font-semibold mb-3 text-lg">Enter Amount (₹)</label>
                            <div class="relative">
                                <span class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500 text-xl font-semibold">₹</span>
                                <input
                                    type="number"
                                    id="paymentAmount"
                                    name="paymentAmount"
                                    class="w-full bg-gray-50 border-2 border-gray-300 rounded-xl pl-12 pr-4 py-4 text-gray-800 text-xl font-semibold placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:bg-white transition-all duration-300 shadow-inner"
                                    placeholder="Enter amount"
                                    min="1"
                                    step="1"
                                    autocomplete="off"
                                    required>
                            </div>
                            <p class="text-gray-600 text-sm mt-2">Minimum amount: ₹1</p>
                        </div>

                        <!-- UPI Details -->
                        <div class="bg-gray-100 rounded-xl p-6 mb-6 border border-gray-200">
                            <h4 class="text-gray-800 font-semibold mb-4 text-center text-lg">UPI Payment Details</h4>

                            <div class="space-y-4">
                                <div class="flex justify-between items-center p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
                                    <span class="text-gray-600 font-medium">UPI ID:</span>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-gray-800 font-mono font-semibold">8792866211@ybl</span>
                                        <button onclick="copyUPI()" class="text-purple-600 hover:text-purple-700 transition-colors p-1 rounded hover:bg-purple-50">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
                                    <span class="text-gray-600 font-medium">Merchant:</span>
                                    <span class="text-gray-800 font-semibold">SkyStack</span>
                                </div>
                            </div>
                        </div>

                        <!-- QR Code Section -->
                        <div class="text-center mb-6">
                            <div class="bg-white rounded-xl p-4 inline-block">
                                <div id="qrcode" class="w-48 h-48 mx-auto bg-gray-100 rounded-lg flex items-center justify-center">
                                    <p class="text-gray-500">QR Code will appear here</p>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm mt-2">Scan with PhonePe App</p>

                            <!-- QR Code Status -->
                            <div id="qrStatus" class="mt-4 hidden">
                                <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                                    <p class="text-green-700 text-sm font-semibold">✅ QR Code Generated Successfully!</p>
                                    <p class="text-gray-600 text-xs mt-1">Scan with any UPI app to pay</p>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="space-y-4">
                            <button id="generateQR" class="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                                Generate QR Code
                            </button>

                            <button onclick="openPhonePe()" class="w-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                                Pay with PhonePe App
                            </button>

                            <!-- Payment Confirmation Button -->
                            <button id="confirmPayment" class="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                                ✅ I Have Completed Payment
                            </button>

                            <p class="text-gray-600 text-sm text-center mt-2">
                                Click above after completing payment via QR code or PhonePe app
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Lavender Footer -->
    <footer class="bg-lavender-700 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Company Info -->
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <img src="/images/skystack-logo.svg" alt="SkyStack Logo" class="h-8">
                    </div>
                    <p class="text-gray-400 mb-4">Code that performs. Design that delights.</p>
                    <p class="text-gray-400">Building the future of web development, one project at a time.</p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                    <ul class="space-y-2">
                        <li><a href="#home" class="text-gray-400 hover:text-white transition-colors">Home</a></li>
                        <li><a href="#about" class="text-gray-400 hover:text-white transition-colors">About</a></li>
                        <li><a href="#services" class="text-gray-400 hover:text-white transition-colors">Services</a></li>
                        <li><a href="#projects" class="text-gray-400 hover:text-white transition-colors">Projects</a></li>
                        <li><a href="#contact" class="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                        <li><a href="#payment" class="text-purple-400 hover:text-purple-300 transition-colors">Payment</a></li>
                    </ul>
                </div>

                <!-- Social & Contact -->
                <div>
                    <h3 class="text-lg font-semibold mb-4">Connect With Us</h3>
                    <p class="text-gray-400 mb-4">✨ Websites that work. 💻 Founder @ SkyStack 📩 DM to build yours.</p>
                    <div class="flex space-x-4">
                        <a href="https://wa.me/918792866211" class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center hover:bg-green-600 transition-colors">
                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.588z"/>
                            </svg>
                        </a>
                        <a href="mailto:<EMAIL>" class="w-10 h-10 bg-sky-blue rounded-full flex items-center justify-center hover:bg-sky-dark transition-colors">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400">© 2025 SkyStack. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="/js/script.js"></script>

    <!-- Form Flow JavaScript -->
    <script>
        // Add a button to the contact section to simulate form completion
        document.addEventListener('DOMContentLoaded', function() {
            // Add form completion button to contact section
            const contactSection = document.getElementById('contact');
            if (contactSection) {
                // Create form completion button
                const formCompletionDiv = document.createElement('div');
                formCompletionDiv.className = 'text-center mt-8';
                formCompletionDiv.innerHTML = `
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4 max-w-md mx-auto">
                        <p class="text-blue-800 font-semibold mb-2">✅ Form Completed?</p>
                        <p class="text-blue-600 text-sm mb-4">Click below after submitting the Google Form</p>
                        <button id="formCompletedBtn" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300">
                            I Completed the Form
                        </button>
                    </div>
                `;

                // Insert after the contact section content
                const contactContainer = contactSection.querySelector('.container');
                if (contactContainer) {
                    contactContainer.appendChild(formCompletionDiv);
                }
            }

            // Form completion handler
            const formCompletedBtn = document.getElementById('formCompletedBtn');
            if (formCompletedBtn) {
                formCompletedBtn.addEventListener('click', function() {
                    // Hide contact section and show WhatsApp contact directly
                    document.getElementById('contact').style.display = 'none';
                    document.getElementById('whatsapp-contact').classList.remove('hidden');

                    // Scroll to WhatsApp contact
                    document.getElementById('whatsapp-contact').scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            }

            // Proceed to Payment handler
            const proceedToPaymentBtn = document.getElementById('proceedToPayment');
            if (proceedToPaymentBtn) {
                proceedToPaymentBtn.addEventListener('click', function() {
                    // Hide WhatsApp contact and show payment section
                    document.getElementById('whatsapp-contact').classList.add('hidden');
                    document.getElementById('payment').classList.remove('hidden');

                    // Scroll to payment
                    document.getElementById('payment').scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            }
        });

        // Payment functionality
        function copyUPI() {
            const upiId = '8792866211@ybl';
            navigator.clipboard.writeText(upiId).then(function() {
                // Show success message
                const button = event.target.closest('button');
                const originalHTML = button.innerHTML;
                button.innerHTML = '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
                button.className = 'text-green-600 p-1 rounded';

                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.className = 'text-purple-600 hover:text-purple-700 transition-colors p-1 rounded hover:bg-purple-50';
                }, 2000);
            });
        }

        function openPhonePe() {
            const amount = document.getElementById('paymentAmount').value;
            if (!amount || amount < 1) {
                alert('Please enter a valid amount');
                return;
            }

            // Show payment initiated message
            const confirmBtn = document.getElementById('confirmPayment');
            confirmBtn.innerHTML = '⏳ Payment Initiated - Complete in PhonePe App';
            confirmBtn.className = 'w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300';

            // PhonePe deep link
            const phonepeUrl = `phonepe://pay?pa=8792866211@ybl&pn=SkyStack&am=${amount}&cu=INR&tn=SkyStack Project Payment`;

            // Try to open PhonePe app
            window.location.href = phonepeUrl;

            // Fallback to UPI generic link after a delay
            setTimeout(() => {
                const upiUrl = `upi://pay?pa=8792866211@ybl&pn=SkyStack&am=${amount}&cu=INR&tn=SkyStack Project Payment`;
                window.location.href = upiUrl;
            }, 1000);

            // Show payment completion button after app opens
            setTimeout(() => {
                confirmBtn.innerHTML = '✅ I Have Completed Payment';
                confirmBtn.className = 'w-full bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg animate-pulse';
            }, 3000);
        }

        // Payment confirmation functionality
        function confirmPayment() {
            const amount = document.getElementById('paymentAmount').value;
            if (!amount || amount < 1) {
                alert('Please enter payment amount first');
                return;
            }

            const confirmBtn = document.getElementById('confirmPayment');

            // Show loading state
            confirmBtn.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Verifying Payment...
            `;

            // Simulate payment verification (in real app, this would check with payment gateway)
            setTimeout(() => {
                // Redirect to payment success page with amount
                window.location.href = `/payment-success?amount=${amount}`;
            }, 3000);
        }

        // Generate QR Code functionality
        document.addEventListener('DOMContentLoaded', function() {
            const generateQRBtn = document.getElementById('generateQR');
            const amountInput = document.getElementById('paymentAmount');
            const confirmPaymentBtn = document.getElementById('confirmPayment');

            // Payment confirmation button handler
            if (confirmPaymentBtn) {
                confirmPaymentBtn.addEventListener('click', confirmPayment);
            }

            if (generateQRBtn && amountInput) {
                generateQRBtn.addEventListener('click', function() {
                    const amount = amountInput.value;

                    if (!amount || amount < 1) {
                        alert('Please enter a valid amount');
                        amountInput.focus();
                        return;
                    }

                    // Show loading state
                    this.innerHTML = `
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Generating QR Code...
                    `;

                    // Generate QR code
                    setTimeout(() => {
                        const upiString = `upi://pay?pa=8792866211@ybl&pn=SkyStack&am=${amount}&cu=INR&tn=SkyStack Project Payment`;

                        // Create QR code using a simple QR code generator
                        const qrContainer = document.getElementById('qrcode');
                        qrContainer.innerHTML = `
                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(upiString)}"
                                 alt="Payment QR Code"
                                 class="w-48 h-48 rounded-lg">
                        `;

                        // Show success status
                        document.getElementById('qrStatus').classList.remove('hidden');

                        // Reset button
                        this.innerHTML = 'Generate QR Code';

                        // Show success message
                        this.innerHTML = '✅ QR Code Generated!';
                        this.className = 'w-full bg-green-600 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300';

                        setTimeout(() => {
                            this.innerHTML = 'Generate New QR Code';
                            this.className = 'w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg';
                        }, 3000);

                    }, 2000);
                });

                // Add visual feedback on focus
                amountInput.addEventListener('focus', function() {
                    this.classList.add('ring-2', 'ring-purple-500', 'border-purple-500');
                    this.classList.remove('border-gray-300');
                });

                amountInput.addEventListener('blur', function() {
                    this.classList.remove('ring-2', 'ring-purple-500', 'border-purple-500');
                    this.classList.add('border-gray-300');
                });
            }
        });
    </script>
</body>
</html>
