<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - SkyStack</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/css/style.css">
</head>
<body class="font-poppins">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-lg border-b border-white/10">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <img src="/images/skystack-icon.svg" alt="SkyStack" class="w-10 h-10">
                    <span class="text-2xl font-bold text-white">SkyStack</span>
                </div>
                <a href="/" class="text-white hover:text-blue-400 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                </a>
            </div>
        </div>
    </nav>

    <!-- Payment Page -->
    <div class="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 pt-20">
        <div class="container mx-auto px-4 py-12">
            <div class="max-w-2xl mx-auto">
                <!-- Header -->
                <div class="text-center mb-12">
                    <h1 class="text-4xl font-bold text-white mb-4">Payment Details</h1>
                    <p class="text-xl text-gray-300">Complete your advance payment securely</p>

                    <!-- Progress Steps -->
                    <div class="flex items-center justify-center mt-8 space-x-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center text-white font-bold">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <span class="ml-2 text-green-400 font-semibold">Project Details</span>
                        </div>
                        <div class="w-8 h-1 bg-green-500 rounded"></div>
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold">2</div>
                            <span class="ml-2 text-white font-semibold">Payment</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Options -->
                <div class="bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-white/20 mb-8">
                    <h2 class="text-2xl font-bold text-white mb-6 text-center">Choose Payment Method</h2>

                    <!-- PhonePe Option -->
                    <div class="payment-option active" data-method="phonepe">
                        <div class="flex items-center justify-between p-4 bg-white/10 rounded-xl border border-white/20 cursor-pointer hover:bg-white/20 transition-all">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold text-lg">₹</span>
                                </div>
                                <div>
                                    <h3 class="text-white font-semibold">PhonePe UPI</h3>
                                    <p class="text-white/70 text-sm">Pay using PhonePe UPI</p>
                                </div>
                            </div>
                            <div class="w-6 h-6 border-2 border-purple-400 rounded-full flex items-center justify-center">
                                <div class="w-3 h-3 bg-purple-400 rounded-full"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Details -->
                <div class="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-purple-400/30">
                    <div class="text-center mb-8">
                        <div class="w-24 h-24 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-2xl ring-4 ring-purple-400/30">
                            <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <h3 class="text-3xl font-bold bg-gradient-to-r from-purple-300 to-pink-300 bg-clip-text text-transparent mb-2">PhonePe Payment</h3>
                        <p class="text-white/80 text-lg">Scan QR or use UPI ID</p>
                    </div>

                    <!-- Amount Input -->
                    <div class="mb-8">
                        <label for="paymentAmount" class="block text-white font-bold mb-4 text-xl flex items-center">
                            <svg class="w-6 h-6 mr-2 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            Enter Amount (₹)
                        </label>
                        <div class="relative">
                            <span class="absolute left-4 top-1/2 transform -translate-y-1/2 text-purple-300 text-2xl font-bold">₹</span>
                            <input
                                type="number"
                                id="paymentAmount"
                                name="paymentAmount"
                                class="w-full bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-2 border-purple-400/50 rounded-xl pl-12 pr-4 py-5 text-white text-2xl font-bold placeholder-white/60 focus:outline-none focus:border-purple-300 focus:bg-gradient-to-r focus:from-purple-500/30 focus:to-blue-500/30 transition-all duration-300 backdrop-blur-sm shadow-lg"
                                placeholder="Enter amount"
                                min="1"
                                step="1"
                                autocomplete="off"
                                required>
                        </div>
                        <p class="text-purple-200 text-sm mt-3 flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Minimum amount: ₹1
                        </p>
                    </div>

                    <!-- UPI Details -->
                    <div class="bg-gradient-to-r from-purple-500/10 to-pink-500/10 rounded-xl p-6 mb-6 border border-purple-400/30 backdrop-blur-sm shadow-lg">
                        <h4 class="text-white font-bold mb-6 text-center text-lg flex items-center justify-center">
                            <svg class="w-6 h-6 mr-2 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                            </svg>
                            UPI Payment Details
                        </h4>

                        <div class="space-y-4">
                            <div class="flex justify-between items-center p-4 bg-gradient-to-r from-purple-600/20 to-blue-600/20 rounded-lg border border-purple-400/40 backdrop-blur-sm shadow-md">
                                <span class="text-purple-200 font-medium flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"></path>
                                    </svg>
                                    UPI ID:
                                </span>
                                <div class="flex items-center space-x-2">
                                    <span class="text-white font-mono font-bold text-lg bg-white/10 px-3 py-1 rounded-lg">8792866211@ybl</span>
                                    <button onclick="copyUPI()" class="text-purple-300 hover:text-white transition-all duration-300 p-2 rounded-lg hover:bg-purple-500/30 transform hover:scale-110">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <div class="flex justify-between items-center p-4 bg-gradient-to-r from-green-600/20 to-teal-600/20 rounded-lg border border-green-400/40 backdrop-blur-sm shadow-md">
                                <span class="text-green-200 font-medium flex items-center">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    Merchant:
                                </span>
                                <span class="text-white font-bold text-lg bg-white/10 px-3 py-1 rounded-lg">SkyStack</span>
                            </div>
                        </div>
                    </div>

                    <!-- QR Code Section -->
                    <div class="text-center mb-8">
                        <div class="bg-gradient-to-br from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-2xl p-6 inline-block border border-purple-400/40 shadow-xl">
                            <div id="qrCodeContainer" class="w-56 h-56 bg-white/90 rounded-xl flex items-center justify-center border-2 border-purple-300/50 shadow-lg">
                                <div id="qrPlaceholder" class="text-center">
                                    <svg class="w-20 h-20 mx-auto mb-3 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M12 12h-4.01M12 12v4m6-4h.01M12 8h.01M12 8h4.01M12 8H7.99"></path>
                                    </svg>
                                    <p class="text-gray-600 text-sm font-medium">QR Code will appear<br>after entering amount</p>
                                </div>
                                <img id="qrCodeImage" class="w-full h-full object-contain hidden rounded-lg" alt="Payment QR Code">
                            </div>
                        </div>
                        <p class="text-purple-200 text-base mt-4 font-medium flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                            Scan with PhonePe App
                        </p>

                        <!-- QR Code Status -->
                        <div id="qrStatus" class="mt-4 hidden">
                            <div class="bg-green-500/20 border border-green-400/30 rounded-lg p-3 backdrop-blur-sm">
                                <p class="text-green-300 text-sm font-semibold">✅ QR Code Generated Successfully!</p>
                                <p class="text-white/60 text-xs mt-1">Scan with any UPI app to pay</p>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-4">
                        <button id="generateQR" class="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                            Generate QR Code
                        </button>

                        <button onclick="openPhonePe()" class="w-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                            Pay with PhonePe App
                        </button>

                        <div class="grid grid-cols-2 gap-4">
                            <a href="/payment-form" class="block text-center bg-white/10 hover:bg-white/20 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 border border-white/20 backdrop-blur-sm">
                                ← Back to Form
                            </a>
                            <a href="/" class="block text-center bg-white/10 hover:bg-white/20 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 border border-white/20 backdrop-blur-sm">
                                Back to Home
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            const amountInput = document.getElementById('paymentAmount');

            // Focus on amount input when page loads
            setTimeout(() => {
                amountInput.focus();
            }, 500);

            // Add input validation and formatting
            amountInput.addEventListener('input', function(e) {
                let value = e.target.value;

                // Remove any non-numeric characters except decimal point
                value = value.replace(/[^0-9.]/g, '');

                // Ensure only one decimal point
                const parts = value.split('.');
                if (parts.length > 2) {
                    value = parts[0] + '.' + parts.slice(1).join('');
                }

                // Limit to 2 decimal places
                if (parts[1] && parts[1].length > 2) {
                    value = parts[0] + '.' + parts[1].substring(0, 2);
                }

                e.target.value = value;
            });

            // Add visual feedback on focus
            amountInput.addEventListener('focus', function() {
                this.classList.add('ring-2', 'ring-purple-500', 'border-purple-500');
                this.classList.remove('border-gray-300');
            });

            amountInput.addEventListener('blur', function() {
                this.classList.remove('ring-2', 'ring-purple-500', 'border-purple-500');
                this.classList.add('border-gray-300');
            });
        });

        function copyUPI() {
            navigator.clipboard.writeText('8792866211@ybl').then(() => {
                alert('UPI ID copied to clipboard!');
            });
        }

        function openPhonePe() {
            const amount = document.getElementById('paymentAmount').value;
            if (!amount || amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }

            // Store amount for success page
            localStorage.setItem('paymentAmount', amount);

            // PhonePe UPI URL scheme
            const upiUrl = `upi://pay?pa=8792866211@ybl&pn=SkyStack&am=${amount}&cu=INR&tn=Advance Payment for SkyStack Services`;

            // Show payment instructions
            const confirmed = confirm(`You will be redirected to PhonePe to pay ₹${amount}.\n\nAfter completing the payment, click OK to continue.`);

            if (confirmed) {
                // Try to open PhonePe app
                window.open(upiUrl, '_blank');

                // Simulate payment completion (in real app, you'd verify with payment gateway)
                setTimeout(() => {
                    const paymentConfirmed = confirm('Have you completed the payment? Click OK if payment was successful.');
                    if (paymentConfirmed) {
                        // Redirect to success page
                        window.location.href = `/payment-success?amount=${amount}&txnId=TXN${Date.now()}`;
                    }
                }, 3000);
            }
        }

        document.getElementById('generateQR').addEventListener('click', async function() {
            const amount = document.getElementById('paymentAmount').value;
            if (!amount || amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }

            // Show loading state
            const button = this;
            const originalText = button.innerHTML;
            button.innerHTML = `
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Generating QR Code...
            `;
            button.disabled = true;

            try {
                // Call the QR generation API
                const response = await fetch('/generate-qr', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ amount: amount })
                });

                const data = await response.json();

                if (data.success) {
                    // Display the QR code
                    const qrImage = document.getElementById('qrCodeImage');
                    const qrPlaceholder = document.getElementById('qrPlaceholder');
                    const qrStatus = document.getElementById('qrStatus');

                    qrImage.src = data.qrCode;
                    qrImage.classList.remove('hidden');
                    qrPlaceholder.classList.add('hidden');
                    qrStatus.classList.remove('hidden');

                    // Update button text
                    button.innerHTML = '✅ QR Code Generated';
                    button.classList.remove('from-purple-600', 'to-blue-600');
                    button.classList.add('from-green-600', 'to-green-700');

                } else {
                    throw new Error(data.error || 'Failed to generate QR code');
                }

            } catch (error) {
                console.error('QR Generation Error:', error);
                alert('Failed to generate QR code. Please try again.');
                button.innerHTML = originalText;
                button.disabled = false;
            }
        });
    </script>
</body>
</html>
