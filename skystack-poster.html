<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SkyStack - Professional Web Development</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;600;700;800&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: white;
            overflow: hidden;
        }
        
        .poster {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
        }
        
        /* Animated background elements */
        .bg-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }
        
        .bg-circle {
            position: absolute;
            border-radius: 50%;
            background: linear-gradient(45deg, #00ADEF20, #00ADEF10);
            animation: float 6s ease-in-out infinite;
        }
        
        .bg-circle:nth-child(1) {
            width: 300px;
            height: 300px;
            top: -150px;
            right: -150px;
            animation-delay: 0s;
        }
        
        .bg-circle:nth-child(2) {
            width: 200px;
            height: 200px;
            bottom: -100px;
            left: -100px;
            animation-delay: 2s;
        }
        
        .bg-circle:nth-child(3) {
            width: 150px;
            height: 150px;
            top: 40%;
            left: 10%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-20px) scale(1.05); }
        }
        
        .content {
            position: relative;
            z-index: 2;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 40px;
        }
        
        /* Header */
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 60px;
        }
        
        .logo-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .logo-placeholder {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #00ADEF, #0088CC);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: 800;
            color: white;
            box-shadow: 0 10px 30px rgba(0, 173, 239, 0.3);
        }
        
        .brand-name {
            font-size: 48px;
            font-weight: 800;
            background: linear-gradient(45deg, #00ADEF, #ffffff);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -2px;
        }
        
        .qr-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        
        .qr-code {
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 12px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0, 173, 239, 0.2);
        }
        
        .qr-code img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .qr-label {
            font-size: 12px;
            color: #00ADEF;
            font-weight: 600;
            text-align: center;
        }
        
        /* Main content */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            gap: 40px;
        }
        
        .tagline {
            font-size: 64px;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ffffff, #00ADEF);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .description {
            font-size: 20px;
            line-height: 1.6;
            max-width: 800px;
            color: #cccccc;
            margin-bottom: 40px;
        }
        
        /* Services grid */
        .services {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            max-width: 900px;
            width: 100%;
        }
        
        .service-item {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 173, 239, 0.2);
            border-radius: 16px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .service-item:hover {
            transform: translateY(-5px);
            border-color: #00ADEF;
            box-shadow: 0 20px 40px rgba(0, 173, 239, 0.2);
        }
        
        .service-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 173, 239, 0.1), transparent);
            transition: left 0.5s ease;
        }
        
        .service-item:hover::before {
            left: 100%;
        }
        
        .service-icon {
            font-size: 32px;
            color: #00ADEF;
        }
        
        .service-name {
            font-size: 16px;
            font-weight: 600;
            color: white;
            text-align: center;
        }
        
        /* Footer */
        .footer {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 40px;
            padding-top: 40px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 16px;
            color: #cccccc;
        }
        
        .contact-icon {
            color: #00ADEF;
            font-size: 20px;
        }
        
        .contact-value {
            font-weight: 600;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .content {
                padding: 20px;
            }
            
            .header {
                flex-direction: column;
                gap: 20px;
                margin-bottom: 40px;
            }
            
            .brand-name {
                font-size: 32px;
            }
            
            .tagline {
                font-size: 36px;
            }
            
            .description {
                font-size: 16px;
            }
            
            .services {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
            
            .footer {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="poster">
        <div class="bg-elements">
            <div class="bg-circle"></div>
            <div class="bg-circle"></div>
            <div class="bg-circle"></div>
        </div>
        
        <div class="content">
            <div class="header">
                <div class="logo-section">
                    <div class="logo-placeholder">S</div>
                    <div class="brand-name">SkyStack</div>
                </div>
                
                <div class="qr-section">
                    <div class="qr-code">
                        <img src="skystack-website-qr.png" alt="QR Code" style="width: 100%; height: 100%; object-fit: contain;">
                    </div>
                    <div class="qr-label">Scan to Visit<br>Our Website</div>
                </div>
            </div>
            
            <div class="main-content">
                <div class="tagline">✨ Websites that work.<br>Built by SkyStack.</div>
                
                <div class="description">
                    SkyStack builds fast, beautiful, and responsive websites for businesses, creators, and startups worldwide. Custom design, smooth performance, and client-focused development — all in one.
                </div>
                
                <div class="services">
                    <div class="service-item">
                        <div class="service-icon">🚀</div>
                        <div class="service-name">Landing Pages</div>
                    </div>
                    <div class="service-item">
                        <div class="service-icon">🎨</div>
                        <div class="service-name">Portfolio Sites</div>
                    </div>
                    <div class="service-item">
                        <div class="service-icon">🏢</div>
                        <div class="service-name">Small Business Sites</div>
                    </div>
                    <div class="service-item">
                        <div class="service-icon">📝</div>
                        <div class="service-name">Blog/News Sites</div>
                    </div>
                    <div class="service-item">
                        <div class="service-icon">💳</div>
                        <div class="service-name">Payment Integration</div>
                    </div>
                    <div class="service-item">
                        <div class="service-icon">⚡</div>
                        <div class="service-name">Custom Web Apps</div>
                    </div>
                </div>
            </div>
            
            <div class="footer">
                <div class="contact-item">
                    <div class="contact-icon">📱</div>
                    <div class="contact-value">WhatsApp: 8792866211</div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">✉️</div>
                    <div class="contact-value"><EMAIL></div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
