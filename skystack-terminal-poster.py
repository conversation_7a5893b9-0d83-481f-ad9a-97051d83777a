#!/usr/bin/env python3
import os
import time
import random

# ANSI color codes for beautiful terminal output
class Colors:
    RESET = '\033[0m'
    BOLD = '\033[1m'
    DIM = '\033[2m'
    
    # Colors
    BLACK = '\033[30m'
    RED = '\033[31m'
    GREEN = '\033[32m'
    YELLOW = '\033[33m'
    BLUE = '\033[34m'
    MAGENTA = '\033[35m'
    CYAN = '\033[36m'
    WHITE = '\033[37m'
    
    # Bright colors
    BRIGHT_BLACK = '\033[90m'
    BRIGHT_RED = '\033[91m'
    BRIGHT_GREEN = '\033[92m'
    BRIGHT_YELLOW = '\033[93m'
    BRIGHT_BLUE = '\033[94m'
    BRIGHT_MAGENTA = '\033[95m'
    BRIGHT_CYAN = '\033[96m'
    BRIGHT_WHITE = '\033[97m'
    
    # Background colors
    BG_BLACK = '\033[40m'
    BG_BLUE = '\033[44m'
    BG_CYAN = '\033[46m'

def clear_screen():
    os.system('cls' if os.name == 'nt' else 'clear')

def print_with_delay(text, delay=0.02):
    for char in text:
        print(char, end='', flush=True)
        time.sleep(delay)
    print()

def create_skystack_poster():
    clear_screen()
    
    # Header with animated border
    border = f"{Colors.BRIGHT_CYAN}{'═' * 80}{Colors.RESET}"
    
    poster = f"""
{border}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                                                                              {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}  {Colors.BRIGHT_BLUE}{Colors.BOLD}███████{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}  {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}   {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}███████{Colors.RESET} {Colors.BRIGHT_BLUE}{Colors.BOLD}████████{Colors.RESET}  {Colors.BRIGHT_CYAN}{Colors.BOLD}█████{Colors.RESET}   {Colors.BRIGHT_BLUE}{Colors.BOLD}██████{Colors.RESET}  {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}  {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET}  {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}  {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET}      {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}  {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}   {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}         {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET}    {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}   {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}   {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}  {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET}  {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}  {Colors.BRIGHT_BLUE}{Colors.BOLD}███████{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}  {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}███████{Colors.RESET} {Colors.BRIGHT_BLUE}{Colors.BOLD}███████{Colors.RESET}    {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}    {Colors.BRIGHT_BLUE}{Colors.BOLD}███████{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}   {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}██████{Colors.RESET}   {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}       {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}  {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET}      {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET}         {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}    {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET}   {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET}   {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET}  {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}  {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}  {Colors.BRIGHT_BLUE}{Colors.BOLD}███████{Colors.RESET}  {Colors.BRIGHT_CYAN}{Colors.BOLD}████{Colors.RESET}       {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}███████{Colors.RESET}    {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET}    {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}   {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}   {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET} {Colors.BRIGHT_CYAN}{Colors.BOLD}██{Colors.RESET}  {Colors.BRIGHT_BLUE}{Colors.BOLD}██{Colors.RESET}  {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                                                                              {Colors.BRIGHT_CYAN}║{Colors.RESET}
{border}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                                                                              {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                    {Colors.BRIGHT_YELLOW}{Colors.BOLD}✨ WEBSITES THAT WORK. BUILT BY SKYSTACK. ✨{Colors.RESET}                   {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                                                                              {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}     {Colors.WHITE}SkyStack builds fast, beautiful, and responsive websites for businesses,{Colors.RESET}     {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}     {Colors.WHITE}creators, and startups worldwide. Custom design, smooth performance,{Colors.RESET}      {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                    {Colors.WHITE}and client-focused development — all in one.{Colors.RESET}                    {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                                                                              {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                              {Colors.BRIGHT_MAGENTA}{Colors.BOLD}🚀 OUR SERVICES 🚀{Colors.RESET}                              {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                                                                              {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}  {Colors.BG_BLUE}{Colors.WHITE} 🚀 Landing Pages {Colors.RESET}     {Colors.BG_BLUE}{Colors.WHITE} 🎨 Portfolio Sites {Colors.RESET}     {Colors.BG_BLUE}{Colors.WHITE} 🏢 Business Sites {Colors.RESET}  {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                                                                              {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}  {Colors.BG_CYAN}{Colors.BLACK} 📝 Blog/News Sites {Colors.RESET}  {Colors.BG_CYAN}{Colors.BLACK} 💳 Payment Integration {Colors.RESET}  {Colors.BG_CYAN}{Colors.BLACK} ⚡ Custom Web Apps {Colors.RESET}  {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                                                                              {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                                                                              {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                            {Colors.BRIGHT_GREEN}{Colors.BOLD}📱 CONTACT US TODAY! 📱{Colors.RESET}                            {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                                                                              {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}              {Colors.BRIGHT_GREEN}📱 WhatsApp: {Colors.BRIGHT_WHITE}{Colors.BOLD}8792866211{Colors.RESET}                                   {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}              {Colors.BRIGHT_GREEN}✉️  Email: {Colors.BRIGHT_WHITE}{Colors.BOLD}<EMAIL>{Colors.RESET}                         {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                                                                              {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                          {Colors.BRIGHT_YELLOW}🌐 Visit: localhost:3000 🌐{Colors.RESET}                          {Colors.BRIGHT_CYAN}║{Colors.RESET}
{Colors.BRIGHT_CYAN}║{Colors.RESET}                                                                              {Colors.BRIGHT_CYAN}║{Colors.RESET}
{border}

{Colors.BRIGHT_MAGENTA}{Colors.BOLD}                    🔥 PROFESSIONAL • FAST • BEAUTIFUL 🔥{Colors.RESET}
"""
    
    return poster

def animate_poster():
    poster = create_skystack_poster()
    print_with_delay(poster, 0.01)
    
    # Add some sparkle effects
    sparkles = [
        f"{Colors.BRIGHT_YELLOW}✨{Colors.RESET}",
        f"{Colors.BRIGHT_CYAN}💫{Colors.RESET}",
        f"{Colors.BRIGHT_MAGENTA}⭐{Colors.RESET}",
        f"{Colors.BRIGHT_WHITE}✦{Colors.RESET}"
    ]
    
    print(f"\n{Colors.BRIGHT_CYAN}{Colors.BOLD}Loading SkyStack magic", end="")
    for i in range(10):
        print(f" {random.choice(sparkles)}", end="", flush=True)
        time.sleep(0.3)
    
    print(f"\n\n{Colors.BRIGHT_GREEN}{Colors.BOLD}🚀 SkyStack Terminal Poster Ready! 🚀{Colors.RESET}")
    print(f"{Colors.BRIGHT_CYAN}Press Ctrl+C to exit{Colors.RESET}")

if __name__ == "__main__":
    try:
        animate_poster()
        # Keep the poster displayed
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print(f"\n\n{Colors.BRIGHT_YELLOW}Thanks for viewing SkyStack! 👋{Colors.RESET}")
        print(f"{Colors.BRIGHT_CYAN}Contact us to build your dream website! 🌟{Colors.RESET}\n")
