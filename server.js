const express = require('express');
const path = require('path');
const fs = require('fs');
const QRCode = require('qrcode');

const app = express();
const PORT = process.env.PORT || 3000;

// Set EJS as templating engine
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Middleware
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// Debug middleware
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    next();
});

// Routes
app.get('/', (req, res) => {
    res.render('index', {
        title: 'SkyStack - Code that performs. Design that delights.',
        page: 'home'
    });
});

app.get('/about', (req, res) => {
    res.render('index', { 
        title: 'About - SkyStack',
        page: 'about'
    });
});

app.get('/services', (req, res) => {
    res.render('index', { 
        title: 'Services - SkyStack',
        page: 'services'
    });
});

app.get('/projects', (req, res) => {
    res.render('index', { 
        title: 'Projects - SkyStack',
        page: 'projects'
    });
});

app.get('/contact', (req, res) => {
    res.render('index', {
        title: 'Contact - SkyStack',
        page: 'contact'
    });
});

app.get('/payment-form', (req, res) => {
    res.render('payment-form', {
        title: 'Payment Process - SkyStack',
        page: 'payment-form'
    });
});

app.get('/payment', (req, res) => {
    console.log('Payment route accessed');
    try {
        res.render('payment', {
            title: 'Payment - SkyStack',
            page: 'payment'
        });
    } catch (error) {
        console.error('Error rendering payment page:', error);
        res.status(500).send('Error loading payment page: ' + error.message);
    }
});

app.get('/payment-success', (req, res) => {
    res.render('payment-success', {
        title: 'Payment Successful - SkyStack',
        page: 'payment-success'
    });
});

app.get('/contact-confirmation', (req, res) => {
    res.render('contact-confirmation', {
        title: 'Contact Confirmation - SkyStack',
        page: 'contact-confirmation'
    });
});

// Test route
app.get('/test-payment', (req, res) => {
    res.send('<h1>Test Payment Route Works!</h1>');
});

// QR Code generation API
app.post('/generate-qr', async (req, res) => {
    try {
        const { amount } = req.body;

        if (!amount || amount <= 0) {
            return res.status(400).json({ error: 'Invalid amount' });
        }

        // UPI payment URL
        const upiUrl = `upi://pay?pa=8792866211@ybl&pn=SkyStack&am=${amount}&cu=INR&tn=Advance Payment for SkyStack Services`;

        // Generate QR code
        const qrCodeDataURL = await QRCode.toDataURL(upiUrl, {
            width: 300,
            margin: 2,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        });

        res.json({
            success: true,
            qrCode: qrCodeDataURL,
            upiUrl: upiUrl,
            amount: amount
        });

    } catch (error) {
        console.error('QR Code generation error:', error);
        res.status(500).json({ error: 'Failed to generate QR code' });
    }
});

// Payment status API
app.post('/payment-status', (req, res) => {
    try {
        const { transactionId, amount } = req.body;

        // In a real application, you would verify the payment with your payment gateway
        // For now, we'll simulate a successful payment

        res.json({
            success: true,
            status: 'completed',
            transactionId: transactionId,
            amount: amount,
            message: 'Payment completed successfully'
        });

    } catch (error) {
        console.error('Payment status error:', error);
        res.status(500).json({ error: 'Failed to check payment status' });
    }
});

// SEO routes
app.get('/robots.txt', (req, res) => {
    res.type('text/plain');
    res.sendFile(path.join(__dirname, 'public', 'robots.txt'));
});

app.get('/sitemap.xml', (req, res) => {
    res.type('application/xml');
    res.sendFile(path.join(__dirname, 'public', 'sitemap.xml'));
});

// Contact form submission
app.post('/contact', (req, res) => {
    const { name, email, project } = req.body;
    
    // Create contact data object
    const contactData = {
        id: Date.now(),
        name: name,
        email: email,
        project: project,
        timestamp: new Date().toISOString(),
        ip: req.ip
    };
    
    // Save to JSON file
    const dataFile = path.join(__dirname, 'data', 'contacts.json');
    
    // Ensure data directory exists
    if (!fs.existsSync(path.join(__dirname, 'data'))) {
        fs.mkdirSync(path.join(__dirname, 'data'));
    }
    
    // Read existing data or create empty array
    let contacts = [];
    if (fs.existsSync(dataFile)) {
        try {
            const data = fs.readFileSync(dataFile, 'utf8');
            contacts = JSON.parse(data);
        } catch (error) {
            console.error('Error reading contacts file:', error);
            contacts = [];
        }
    }
    
    // Add new contact
    contacts.push(contactData);
    
    // Save updated data
    try {
        fs.writeFileSync(dataFile, JSON.stringify(contacts, null, 2));
        console.log('New contact submission:', contactData);
        
        res.json({ 
            success: true, 
            message: 'Thank you for your message! We will get back to you soon.' 
        });
    } catch (error) {
        console.error('Error saving contact:', error);
        res.status(500).json({ 
            success: false, 
            message: 'Sorry, there was an error submitting your message. Please try again.' 
        });
    }
});

// 404 handler
app.use((req, res) => {
    res.status(404).render('index', { 
        title: '404 - Page Not Found - SkyStack',
        page: '404'
    });
});

// Error handler
app.use((error, req, res, next) => {
    console.error('Server error:', error);
    res.status(500).render('index', { 
        title: 'Error - SkyStack',
        page: 'error'
    });
});

// Start server
const server = app.listen(PORT, () => {
    console.log(`🚀 SkyStack server running on http://localhost:${PORT}`);
    console.log(`📁 Serving from: ${__dirname}`);
});

// Keep server alive
server.on('error', (error) => {
    console.error('Server error:', error);
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Process terminated');
    });
});

process.on('SIGINT', () => {
    console.log('SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('Process terminated');
    });
});
