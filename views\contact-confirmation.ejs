<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Confirmation - SkyStack</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/css/style.css">
</head>
<body class="font-poppins">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-lg border-b border-white/10">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <img src="/images/skystack-icon.svg" alt="SkyStack" class="w-10 h-10">
                    <span class="text-2xl font-bold text-white">SkyStack</span>
                </div>
                <a href="/" class="text-white hover:text-blue-400 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                </a>
            </div>
        </div>
    </nav>

    <!-- Contact Confirmation Page -->
    <div class="min-h-screen gradient-bg-1 pt-20">
        <div class="container mx-auto px-4 py-12">
            <div class="max-w-3xl mx-auto">
                
                <!-- Header -->
                <div class="text-center mb-12">
                    <div class="w-24 h-24 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                    </div>
                    <h1 class="text-4xl font-bold text-white mb-4">Ready to Contact Us! 📞</h1>
                    <p class="text-xl text-white/80">Your project requirements have been submitted. Now let's discuss your project details and confirm your order.</p>
                </div>

                <!-- Contact Methods -->
                <div class="grid md:grid-cols-2 gap-6 mb-8">
                    
                    <!-- WhatsApp Contact -->
                    <div class="bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl p-6 border border-white/20">
                        <div class="text-center mb-6">
                            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.785"/>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-2">WhatsApp Chat</h3>
                            <p class="text-white/80 text-sm mb-4">Quick response via WhatsApp</p>
                        </div>
                        
                        <a href="https://wa.me/919876543210?text=Hi! I just submitted my project requirements form and would like to discuss my project details and payment." 
                           target="_blank" 
                           class="block w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 text-center">
                            Chat on WhatsApp
                        </a>
                    </div>

                    <!-- Email Contact -->
                    <div class="bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl p-6 border border-white/20">
                        <div class="text-center mb-6">
                            <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-2">Email Us</h3>
                            <p class="text-white/80 text-sm mb-4">Detailed discussion via email</p>
                        </div>
                        
                        <a href="mailto:<EMAIL>?subject=Project Order Confirmation - Form Submitted&body=Hi SkyStack Team,%0D%0A%0D%0AI just submitted my project requirements form and would like to discuss:%0D%0A%0D%0A1. Project details and timeline%0D%0A2. Final pricing and payment options%0D%0A3. Next steps to get started%0D%0A%0D%0APlease get back to me at your earliest convenience.%0D%0A%0D%0AThank you!" 
                           class="block w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 text-center">
                            Send Email
                        </a>
                    </div>
                </div>

                <!-- What to Expect -->
                <div class="bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-white/20 mb-8">
                    <h2 class="text-2xl font-bold text-white mb-6 text-center">What to Expect Next</h2>
                    
                    <div class="space-y-6">
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                                <span class="text-white font-bold">1</span>
                            </div>
                            <div>
                                <h3 class="text-white font-semibold mb-2">Project Discussion (Within 2 Hours)</h3>
                                <p class="text-white/80">We'll review your requirements and discuss project scope, timeline, and any clarifications needed.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                                <span class="text-white font-bold">2</span>
                            </div>
                            <div>
                                <h3 class="text-white font-semibold mb-2">Quote & Payment Details</h3>
                                <p class="text-white/80">We'll provide final pricing, payment options (advance + milestone payments), and project timeline.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0">
                                <span class="text-white font-bold">3</span>
                            </div>
                            <div>
                                <h3 class="text-white font-semibold mb-2">Secure Payment & Project Start</h3>
                                <p class="text-white/80">Once you confirm, we'll send secure payment links and immediately start working on your project.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Alternative Contact Info -->
                <div class="bg-white/5 backdrop-blur-lg rounded-xl p-6 border border-white/10 text-center">
                    <h3 class="text-white font-semibold mb-4">Alternative Contact Methods</h3>
                    <div class="grid md:grid-cols-3 gap-4 text-white/80">
                        <div>
                            <p class="font-semibold">Phone</p>
                            <p>+91 98765 43210</p>
                        </div>
                        <div>
                            <p class="font-semibold">Email</p>
                            <p><EMAIL></p>
                        </div>
                        <div>
                            <p class="font-semibold">Response Time</p>
                            <p>Within 2 hours</p>
                        </div>
                    </div>
                </div>

                <!-- Back to Home -->
                <div class="text-center mt-8">
                    <a href="/" class="inline-flex items-center text-white/80 hover:text-white transition-colors">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
