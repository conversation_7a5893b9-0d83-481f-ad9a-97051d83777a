/* Custom CSS for SkyStack Website - Professional Lavender Theme */

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Professional subtle animations */
@keyframes professionalFloat {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.4;
    }
    50% {
        transform: translateY(-10px) scale(1.05);
        opacity: 0.6;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleBounce {
    0% {
        opacity: 0;
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Professional hover effects */
.professional-hover {
    transition: all 0.3s ease;
}

.professional-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(139, 92, 246, 0.15);
}

/* Advanced Background Animations - Professional Business Colors */
.animated-bg {
    background: linear-gradient(-45deg, #1e3a8a, #3730a3, #1e40af, #2563eb, #0ea5e9, #0284c7);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Particle Background */
.particles-bg {
    position: relative;
    overflow: hidden;
}

.particles-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
    animation: particleFloat 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-30px) rotate(120deg); }
    66% { transform: translateY(30px) rotate(240deg); }
}

/* Geometric Background Patterns - Professional Business Colors */
.geometric-bg {
    background-color: #0f172a;
    background-image:
        linear-gradient(30deg, #1e293b 12%, transparent 12.5%, transparent 87%, #1e293b 87.5%, #1e293b),
        linear-gradient(150deg, #1e293b 12%, transparent 12.5%, transparent 87%, #1e293b 87.5%, #1e293b),
        linear-gradient(30deg, #334155 12%, transparent 12.5%, transparent 87%, #334155 87.5%, #334155),
        linear-gradient(150deg, #334155 12%, transparent 12.5%, transparent 87%, #334155 87.5%, #334155);
    background-size: 80px 140px;
    background-position: 0 0, 0 0, 40px 70px, 40px 70px;
}

/* Floating Elements */
.floating {
    animation: floating 6s ease-in-out infinite;
}

@keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Advanced Gradient Backgrounds - Professional Business Colors */
.gradient-bg-1 {
    background: linear-gradient(135deg, #1e40af 0%, #3730a3 100%);
}

.gradient-bg-2 {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

.gradient-bg-3 {
    background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
}

.gradient-bg-4 {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
}

/* Mesh Gradient Background - Professional Business Colors */
.mesh-bg {
    background: radial-gradient(at 40% 20%, hsla(220,100%,50%,0.8) 0px, transparent 50%),
                radial-gradient(at 80% 0%, hsla(210,100%,45%,0.8) 0px, transparent 50%),
                radial-gradient(at 0% 50%, hsla(200,100%,40%,0.6) 0px, transparent 50%),
                radial-gradient(at 80% 50%, hsla(230,100%,55%,0.7) 0px, transparent 50%),
                radial-gradient(at 0% 100%, hsla(240,100%,35%,0.8) 0px, transparent 50%),
                radial-gradient(at 80% 100%, hsla(220,100%,60%,0.6) 0px, transparent 50%),
                linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #0ea5e9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0284c7;
}

/* Loading animation */
.loading {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Navbar scroll effect */
.navbar-scrolled {
    background: rgba(15, 23, 42, 0.98) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* Navigation enhancements */
.nav-link {
    font-weight: 500;
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #3b82f6, #60a5fa);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::before {
    width: 100%;
}

/* Logo animation */
.logo-icon {
    transition: all 0.3s ease;
    filter: drop-shadow(0 0 10px rgba(30, 144, 255, 0.3));
}

.logo-icon:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 0 15px rgba(30, 144, 255, 0.5));
}

/* Card logo showcase styling */
.card-logo-container {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(30, 144, 255, 0.1), rgba(255, 255, 255, 0.05));
}

.card-logo-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(30, 144, 255, 0.1) 0%, transparent 70%);
    animation: backgroundPulse 4s ease-in-out infinite;
    z-index: 1;
}

.card-logo-container > div {
    position: relative;
    z-index: 2;
}

@keyframes backgroundPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.6;
    }
}

.logo-showcase {
    position: relative;
    display: inline-block;
}

.logo-glow-ring {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 120px;
    height: 120px;
    border: 2px solid rgba(30, 144, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulseRing 3s ease-in-out infinite;
}

.card-logo {
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 0 25px rgba(30, 144, 255, 0.6))
            drop-shadow(0 0 50px rgba(255, 255, 255, 0.3));
    animation: logoRotateFloat 6s ease-in-out infinite;
    transition: all 0.4s ease;
}

.card-logo:hover {
    transform: scale(1.15) rotate(5deg);
    filter: drop-shadow(0 0 35px rgba(30, 144, 255, 0.8))
            drop-shadow(0 0 70px rgba(255, 255, 255, 0.5));
}

@keyframes pulseRing {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.3;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.6;
    }
}

@keyframes logoRotateFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    25% {
        transform: translateY(-8px) rotate(2deg);
    }
    50% {
        transform: translateY(-12px) rotate(0deg);
    }
    75% {
        transform: translateY(-8px) rotate(-2deg);
    }
}

/* Hero logo styling */
.hero-logo {
    filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.3));
    animation: heroLogoFloat 4s ease-in-out infinite;
    transition: all 0.3s ease;
}

.hero-logo:hover {
    transform: scale(1.1);
    filter: drop-shadow(0 0 40px rgba(255, 255, 255, 0.5));
}

@keyframes heroLogoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

/* Hero section animations */
.hero-title {
    animation: fadeInUp 1s ease-out;
}

.hero-subtitle {
    animation: fadeInUp 1s ease-out 0.2s both;
}

.hero-cta {
    animation: fadeInUp 1s ease-out 0.4s both;
}

/* Typing Animation */
.typing-animation {
    position: relative;
    display: inline-block;
}

.typing-animation::after {
    content: '|';
    color: #0ea5e9;
    animation: blink 1s infinite;
    margin-left: 2px;
}

.typing-animation.typing-complete::after {
    display: none;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Glowing Effects */
.glow {
    box-shadow: 0 0 20px rgba(14, 165, 233, 0.5);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(14, 165, 233, 0.5); }
    to { box-shadow: 0 0 30px rgba(14, 165, 233, 0.8); }
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Slide In Animations */
.slide-in-left {
    animation: slideInLeft 1s ease-out;
}

.slide-in-right {
    animation: slideInRight 1s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Bounce Animation */
.bounce {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0,-30px,0); }
    70% { transform: translate3d(0,-15px,0); }
    90% { transform: translate3d(0,-4px,0); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Service cards hover effects */
.service-card {
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.service-card:hover .service-icon {
    transform: scale(1.1);
}

.service-icon {
    transition: transform 0.3s ease;
}

/* Project cards hover effects */
.project-card {
    transition: all 0.3s ease;
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Form styling */
.form-input:focus {
    outline: none;
    border-color: #0ea5e9;
    box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

/* Button hover effects */
.btn-primary {
    background: linear-gradient(135deg, #0ea5e9, #0284c7);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(14, 165, 233, 0.3);
}

/* Gradient text */
.gradient-text {
    background: linear-gradient(135deg, #0ea5e9, #0284c7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Section animations */
.section-animate {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-out;
}

.section-animate.in-view {
    opacity: 1;
    transform: translateY(0);
}

/* Mobile menu animation */
.mobile-menu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.mobile-menu.open {
    max-height: 300px;
}

/* Contact form success/error messages */
.form-message {
    padding: 12px 16px;
    border-radius: 8px;
    margin-top: 16px;
    font-weight: 500;
}

.form-message.success {
    background-color: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.form-message.error {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
}

/* Loading spinner */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0ea5e9;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design improvements */
@media (max-width: 768px) {
    .hero-title {
        font-size: 3rem;
    }
    
    .section-padding {
        padding: 60px 0;
    }
    
    .service-card,
    .project-card {
        margin-bottom: 20px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .container {
        padding-left: 16px;
        padding-right: 16px;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
a:focus,
button:focus,
input:focus,
textarea:focus {
    outline: 2px solid #0ea5e9;
    outline-offset: 2px;
}

/* Scroll Progress Indicator */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 4px;
    background: linear-gradient(90deg, #0ea5e9, #0284c7);
    z-index: 9999;
    transition: width 0.3s ease;
}

/* Dark Mode Styles */
.dark {
    background-color: #0f0f23;
    color: #ffffff;
}

.dark .bg-white {
    background-color: #1a1a2e !important;
}

.dark .text-gray-900 {
    color: #ffffff !important;
}

.dark .text-gray-700 {
    color: #e2e8f0 !important;
}

.dark .text-gray-600 {
    color: #cbd5e0 !important;
}

.dark .border-gray-300 {
    border-color: #4a5568 !important;
}

/* Dark Mode Toggle - Professional Colors */
.dark-mode-toggle {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 1000;
    background: linear-gradient(135deg, #1e40af, #3730a3);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.dark-mode-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    transition: opacity 0.5s ease;
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loader {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(255, 255, 255, 0.3);
    border-top: 5px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Animated Counters */
.counter {
    font-size: 3rem;
    font-weight: bold;
    color: #0ea5e9;
}

/* Parallax Effect */
.parallax {
    transform: translateZ(0);
    transition: transform 0.1s ease-out;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
    }

    h1, h2, h3 {
        page-break-after: avoid;
    }
}
