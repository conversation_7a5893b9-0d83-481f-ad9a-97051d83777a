# SkyStack Website

A modern, responsive website for SkyStack - a web development studio that builds fast, responsive, and beautiful websites for clients worldwide.

## 🚀 Features

- **Modern Design**: Clean, professional design with smooth animations
- **Responsive**: Fully responsive design that works on all devices
- **Fast Loading**: Optimized for performance with minimal dependencies
- **SEO Optimized**: Includes meta tags, sitemap, and robots.txt
- **Contact Form**: Working contact form with backend storage
- **Smooth Scrolling**: Smooth navigation between sections
- **Interactive Elements**: Hover effects and animations

## 🛠️ Tech Stack

- **Backend**: Node.js with Express
- **Templating**: EJS (Embedded JavaScript)
- **Styling**: Tailwind CSS + Custom CSS
- **JavaScript**: Vanilla JS for interactivity
- **Data Storage**: JSON file storage for contact submissions

## 📁 Project Structure

```
skystack/
├── server.js              # Main Express server
├── package.json           # Dependencies and scripts
├── views/
│   └── index.ejs          # Main template file
├── public/
│   ├── css/
│   │   └── style.css      # Custom styles
│   ├── js/
│   │   └── script.js      # JavaScript functionality
│   ├── images/            # Images and favicon
│   ├── robots.txt         # SEO robots file
│   └── sitemap.xml        # SEO sitemap
├── data/
│   └── contacts.json      # Contact form submissions
└── README.md              # This file
```

## 🚀 Getting Started

### Prerequisites

- Node.js (v14 or higher)
- npm (comes with Node.js)

### Installation

1. **Clone or download the project**
   ```bash
   cd skystack
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```
   
   Or for production:
   ```bash
   npm start
   ```

4. **Open your browser**
   Navigate to `http://localhost:3000`

## 📧 Contact Form

The contact form automatically saves submissions to `data/contacts.json`. Each submission includes:
- Name
- Email
- Project description
- Timestamp
- IP address

## 🎨 Customization

### Adding Your Logo

1. Replace the placeholder logo in the header section of `views/index.ejs`
2. Add your logo file to `public/images/`
3. Update the favicon by replacing `public/images/favicon.ico`

### Updating Content

- **Company Info**: Edit the content in `views/index.ejs`
- **Services**: Modify the services section with your offerings
- **Projects**: Update the projects section with your portfolio
- **Contact Info**: Update WhatsApp number and email in the contact section

### Styling

- **Colors**: Update the Tailwind config in `views/index.ejs` or modify `public/css/style.css`
- **Fonts**: Change Google Fonts imports in the head section
- **Animations**: Modify CSS animations in `public/css/style.css`

## 🌐 Deployment

### Local Development
```bash
npm run dev
```

### Production
```bash
npm start
```

### Environment Variables
You can set the PORT environment variable:
```bash
PORT=8080 npm start
```

## 📱 Mobile Responsive

The website is fully responsive and includes:
- Mobile-first design approach
- Responsive navigation menu
- Optimized touch interactions
- Proper viewport settings

## 🔧 Performance Optimizations

- Minimal external dependencies
- Optimized images and assets
- Efficient CSS and JavaScript
- Proper caching headers
- SEO-friendly structure

## 📞 Contact Information

- **WhatsApp**: +91 **********
- **Email**: <EMAIL>
- **Instagram**: ✨ Websites that work. 💻 Founder @ SkyStack 📩 DM to build yours.

## 📄 License

This project is licensed under the ISC License.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

---

**SkyStack** - Clean code. Global reach. 🚀
