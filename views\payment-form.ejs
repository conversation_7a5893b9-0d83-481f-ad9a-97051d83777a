<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Process - SkyStack</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/css/style.css">
</head>
<body class="font-poppins">
    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 z-50 bg-black/20 backdrop-blur-lg border-b border-white/10">
        <div class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <img src="/images/skystack-icon.svg" alt="SkyStack" class="w-10 h-10">
                    <span class="text-2xl font-bold text-white">SkyStack</span>
                </div>
                <a href="/" class="text-white hover:text-blue-400 transition-colors">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                </a>
            </div>
        </div>
    </nav>

    <!-- Payment Process Page -->
    <div class="min-h-screen gradient-bg-1 pt-20">
        <div class="container mx-auto px-4 py-12">
            <div class="max-w-4xl mx-auto">
                <!-- Header -->
                <div class="text-center mb-12">
                    <h1 class="text-4xl font-bold text-white mb-4">Payment Process</h1>
                    <p class="text-xl text-white/80">Complete your project details and advance payment</p>

                    <!-- Progress Steps -->
                    <div class="flex items-center justify-center mt-8 space-x-4">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold">1</div>
                            <span class="ml-2 text-white font-semibold">Project Details</span>
                        </div>
                        <div class="w-8 h-1 bg-white/30 rounded"></div>
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center text-white font-bold">2</div>
                            <span class="ml-2 text-white/70">Payment</span>
                        </div>
                    </div>
                </div>

                <!-- Google Form Section -->
                <div class="bg-white/10 backdrop-blur-lg rounded-2xl shadow-2xl p-8 border border-white/20 mb-8">
                    <div class="text-center mb-8">
                        <div class="w-20 h-20 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-white mb-2">Step 1: Project Details</h2>
                        <p class="text-white/80">Please fill out your project requirements and contact information</p>
                    </div>

                    <!-- Google Form Embed -->
                    <div class="bg-white rounded-xl p-6 mb-6">
                        <div class="text-center">
                            <h3 class="text-xl font-bold text-gray-800 mb-4">SkyStack Project Requirements Form</h3>
                            <p class="text-gray-600 mb-6">Please provide your project details so we can give you the best service</p>

                            <!-- Placeholder for Google Form - Replace with your actual Google Form embed -->
                            <div class="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8">
                                <svg class="w-16 h-16 mx-auto text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <p class="text-gray-500 mb-4">Google Form will be embedded here</p>
                                <p class="text-sm text-gray-400">Replace this section with your actual Google Form embed code</p>
                            </div>

                            <!-- Temporary Form Link Button -->
                            <div class="mt-6">
                                <a href="#" id="googleFormBtn" target="_blank" class="inline-flex items-center bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                    </svg>
                                    Open Google Form
                                </a>
                                <p class="text-sm text-gray-500 mt-2">Form will open in a new tab</p>

                                <!-- Form Completion Button (hidden initially) -->
                                <div id="formCompletionSection" class="mt-4 hidden">
                                    <button id="formCompletedBtn" class="inline-flex items-center bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        I Completed the Form
                                    </button>
                                    <p class="text-sm text-gray-500 mt-2">Click after filling out the Google Form</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Form Completion Check -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <svg class="w-5 h-5 text-yellow-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <p class="text-yellow-800 font-semibold">Please complete the form above before proceeding to contact us</p>
                        </div>
                    </div>

                    <!-- Proceed to Contact Button -->
                    <div class="text-center">
                        <button id="proceedToContact" class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                            <svg class="w-5 h-5 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                            Contact Us for Order Confirmation
                        </button>
                        <p class="text-white/70 text-sm mt-2">Complete the form to enable this button</p>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="bg-white/5 backdrop-blur-lg rounded-xl p-6 border border-white/10">
                    <h3 class="text-white font-semibold mb-4">What happens next?</h3>
                    <div class="space-y-3 text-white/80">
                        <div class="flex items-start">
                            <span class="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 mt-0.5">1</span>
                            <p>Fill out the project requirements form with your details</p>
                        </div>
                        <div class="flex items-start">
                            <span class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 mt-0.5">2</span>
                            <p>Contact us through the contact section for order confirmation and payment details</p>
                        </div>
                        <div class="flex items-start">
                            <span class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 mt-0.5">3</span>
                            <p>We'll discuss your project, confirm pricing, and arrange secure payment</p>
                        </div>
                        <div class="flex items-start">
                            <span class="w-6 h-6 bg-orange-600 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 mt-0.5">4</span>
                            <p>Once payment is confirmed, we'll start working on your project immediately</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simulate form completion check
        let formCompleted = false;

        document.getElementById('googleFormBtn').addEventListener('click', function(e) {
            e.preventDefault();

            // Replace this URL with your actual Google Form URL
            const googleFormUrl = 'https://forms.google.com/d/e/1FAIpQLSe26RFGrvIBDwVTvRixPBNtM3EV_uEEvvfXAgg1BCHUciVjOQ/viewform';

            // Open the Google Form in a new tab
            window.open(googleFormUrl, '_blank');

            // Show instructions to user
            alert('Please fill out the form in the new tab, then come back here and click "I completed the form" button below.');

            // Show completion button
            showFormCompletionButton();
        });

        function showFormCompletionButton() {
            const section = document.getElementById('formCompletionSection');
            section.classList.remove('hidden');
        }

        function enableContactButton() {
            const button = document.getElementById('proceedToContact');
            button.disabled = false;
            button.innerHTML = `
                <svg class="w-5 h-5 mr-2 inline-block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                Contact Us for Order Confirmation
            `;

            // Update instructions
            const instructions = document.querySelector('.text-white\\/70');
            if (instructions) {
                instructions.textContent = 'Form completed! Click to contact us for order confirmation';
                instructions.className = 'text-green-400 text-sm mt-2';
            }

            // Update form completion check section
            const formCheck = document.querySelector('.bg-yellow-50');
            if (formCheck) {
                formCheck.className = 'bg-green-50 border border-green-200 rounded-lg p-4 mb-6';
                formCheck.innerHTML = `
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <p class="text-green-800 font-semibold">Form completed! You can now contact us for order confirmation.</p>
                    </div>
                `;
            }
        }

        // Form completion button event listener
        document.addEventListener('DOMContentLoaded', function() {
            const formCompletedBtn = document.getElementById('formCompletedBtn');
            if (formCompletedBtn) {
                formCompletedBtn.addEventListener('click', function() {
                    formCompleted = true;
                    enableContactButton();

                    // Hide the completion button
                    document.getElementById('formCompletionSection').style.display = 'none';
                });
            }
        });

        document.getElementById('proceedToContact').addEventListener('click', function() {
            if (formCompleted) {
                // Add loading state
                this.innerHTML = `
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Redirecting to Contact...
                `;

                // Redirect to contact confirmation page
                setTimeout(() => {
                    window.location.href = '/contact-confirmation';
                }, 1500);
            }
        });
    </script>
</body>
</html>